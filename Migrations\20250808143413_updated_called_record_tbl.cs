﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class updated_called_record_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_called_record_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "InitiatedAt",
                schema: _Schema,
                table: "CallRecords",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InitiatedBy",
                schema: _Schema,
                table: "CallRecords",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InitiatedAt",
                schema: _Schema,
                table: "CallRecords");

            migrationBuilder.DropColumn(
                name: "InitiatedBy",
                schema: _Schema,
                table: "CallRecords");
        }
    }
}
