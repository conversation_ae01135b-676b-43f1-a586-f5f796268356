#region Using Statements
using AutoMapper;
using Hangfire;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Calender.CalenderHelpers;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.Models.RTC.Requests;
using Jobid.App.Calender.Models.RTC.Responses;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using MongoDB.Driver;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using static Jobid.App.Helpers.Utils.Extensions;
using static Jobid.App.JobProject.Enums.Enums;
using File = System.IO.File;
using Microsoft.AspNetCore.Http;
using System.Threading;
using Jobid.App.Helpers.Services.Contract;
using DinkToPdf.Contracts;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.Graph.Models.Security;
using Microsoft.Graph.Models;
using Microsoft.AspNetCore.SignalR;
using Jobid.App.Notification.Hubs;
using Jobid.App.Tenant.ViewModel;
using static Jobid.App.Calender.ViewModel.HasMeetingHappenedDto;
#endregion

namespace Jobid.App.Calender.Services.Implementations
{
    public class CalenderService : ICalenderService
    {
        #region Constructor And Properties
        private JobProDbContext Db;
        public JobProDbContext Dbo;
        private IMapper _mapper;
        private readonly UserManager<Helpers.Models.User> _userManager;
        private readonly IEmailService _emailService;
        private readonly IBackGroundService _backGroundService;
        private readonly IWebHostEnvironment _environment;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IAdminService _adminService;
        private readonly IMemoryCache _cache;
        private readonly ILogService _logService;
        private readonly IRedisCacheService _redisCacheService;
        private readonly string _redisKey;
        private readonly IApiCallService _apiCallService;
        private readonly RTCEndpoints _rtcEndpoints;
        private readonly string _conString;
        private readonly string _meetingBaseURL;
        private readonly IPdfService _pdfService;
        private readonly IPdfGeneratorServices _pdfGeneratorServices;
        private readonly IHubContext<NotificationHub, INotificationClient> _hubContext;

        public CalenderService(JobProDbContext _db,
            JobProDbContext publicSchemaContext,
            IMapper mapper,
            UserManager<Helpers.Models.User> usermanager,
            IEmailService emailService,
            IBackGroundService backGroundService,
            IWebHostEnvironment environment,
            IAWSS3Sevices aWSS3Sevices,
            IAdminService adminService,
            IMemoryCache cache,
            ILogService logService,
            IRedisCacheService redisCacheService,
            IApiCallService apiCallService,
            IPdfService pdfService,
            IPdfGeneratorServices pdfGeneratorServices,
            IHubContext<NotificationHub, INotificationClient> hubContext)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _mapper = mapper;
            _userManager = usermanager;
            _emailService = emailService;
            _backGroundService = backGroundService;
            _environment = environment;
            _aWSS3Sevices = aWSS3Sevices;
            _adminService = adminService;
            _cache = cache;
            _logService = logService;
            _redisCacheService = redisCacheService;
            _redisKey = $"{GlobalVariables.Subdomain}-calender";
            _apiCallService = apiCallService;
            _rtcEndpoints = GlobalVariables.CustomAppSettings.RTCEndpoints;
            _conString = GlobalVariables.ConnectionString;
            _meetingBaseURL = Utility.Constants.MEETING_BASE_URL;
            _pdfService = pdfService;
            _pdfGeneratorServices = pdfGeneratorServices;
            _hubContext = hubContext;
        }
        #endregion

        #region Create Meeting - Internal
        /// <summary>
        /// Adds a schedule to the calender
        /// </summary>
        /// <param name="calenderVm"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<CalenderMeeting> CreateMeeting(CalenderVm calenderVm)
        {
            if (string.IsNullOrEmpty(calenderVm.Frequency) && calenderVm.CustomFrequency is null)
                throw new DirtyFormException("Both Frequency and Custom Frequency connot be null");
            if (!string.IsNullOrEmpty(calenderVm.Frequency) && calenderVm.CustomFrequency is not null)
                throw new DirtyFormException("Only one of Frequency or Custom Frequency can be selected. Kindly select either a Frequency or choose a Custom Frequency");

            var creatorTypeAnAdmin = false;
            if (calenderVm.MeetingOwnerType == MeetingOwnerTypes.Admin)
            {
                if (string.IsNullOrEmpty(calenderVm.AssignedMeetingOwner.Value.ToString()))
                {
                    throw new DirtyFormException("Meeting Owner connot be null for this kind of meeting");
                }
                else
                {
                    // a validation of the CalenderVm.UserId is supposed to be validated to
                    // ensure the person creating the meeting for the meeting owner is an admin
                    var userRole = await _adminService.GetUserRole(calenderVm.UserId.ToString());
                    if (userRole != DataSeeder.SuperAdmin && userRole != DataSeeder.Admin)
                        throw new UnauthorizedAccessException("You do not have the permission to perform this action. You must be an admin or super admin");

                    creatorTypeAnAdmin = true;
                }
            }

            // Get the invitee name
            var userId = creatorTypeAnAdmin ? calenderVm.AssignedMeetingOwner.Value.ToString() : calenderVm.UserId.ToString();
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == userId).FirstOrDefault();
            if (inviteeDetails == null)
                throw new RecordNotFoundException("User not found");

            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
            var inviteeEmail = inviteeDetails.Email;

            // Get user permissions and check if it has permissions to create meetings
            if (calenderVm.CreatedByAI)
            {
                var hasPermission = await CheckUserPermission(userId, Permissions.can_create_meetings);
                if (!hasPermission)
                    throw new UnauthorizedAccessException("You do not have the permission to create a meeting");
            }

            int result;
            int duration = Convert.ToInt32((calenderVm.EndTime - calenderVm.StartDate).TotalMinutes);
            if (duration % 60 == 0)
                calenderVm.MeetingDuration = duration.ToString() + " mins";
            else
                calenderVm.MeetingDuration = (duration / 60).ToString() + "hrs " + (duration % 60).ToString() + "mins";

            var meeting = new CalenderMeeting();
            meeting = _mapper.Map<CalenderMeeting>(calenderVm);
            meeting.Name = meeting.Name.CapitalizeFirstLetterOfFirstWord();

            // Call the RTC endpoint to create the meeting
            var tenantId = await Dbo.Tenants.Where(x => x.Subdomain.ToLower() == calenderVm.SubDomain.ToLower())
                .Select(x => x.Id).FirstOrDefaultAsync();

            var meetingId = GenerateMeetingId();
            meeting.MeetingId = meetingId;
            meeting.MeetLength = duration;
            meeting.CreatedBy = Guid.Parse(userId);
            meeting.MeetingLink = GenerateMeetingLink(calenderVm.SubDomain, meetingId);

            var customFreq = new CustomFrequency();
            if (calenderVm.Frequency == null)
            {
                CustomFrequencyValidations(calenderVm.CustomFrequency, calenderVm.StartDate);

                meeting.HasCustomFrequency = true;
                Db.CalenderMeetings.Add(meeting);
                customFreq = _mapper.Map<CustomFrequency>(calenderVm.CustomFrequency);
                customFreq.CalenderMeetingId = meeting.Id;
                Db.CustomFrequency.Add(customFreq);
            }
            else
                Db.CalenderMeetings.Add(meeting);

            result = await Db.SaveChangesAsync();

            //Convert Base64 to iFormFile and upload
            var fileURLs = new List<string>();
            if (calenderVm?.AttachmentBase64 is not null && (bool)calenderVm?.AttachmentBase64.Any())
            {
                //Convert BAse64 from Client to iFormFile
                var attachments = new List<IFormFile>();
                foreach (var attachment in calenderVm.AttachmentBase64)
                {
                    var file = Utility.ConvertBase64ToFile(attachment);
                    attachments.Add(file);
                };

                //Upload the IFormFile using initial Upload Document Service
                var uploads = await UploadDocument(new UploadDocumentDto()
                {
                    MeetingId = meeting.Id.ToString(),
                    Files = attachments,
                    ReoccuringDeleteOptions = calenderVm.ReoccuringDeleteOptions,
                });

                //Get Signed URL for each uploaded file to be used in email notification
                foreach (var file in uploads)
                {
                    fileURLs.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
                };
                meeting.AttachmentUrls = fileURLs;

            }
            var inviteeEmails = new List<string>() { inviteeEmail };
            var attendees = new Dictionary<string, string>();
            if (result > 0)
            {
                var invitedMmemberEmails = new List<string>();
                var invitedMmemberNames = new List<string>();
                var invitedUsers = new List<UserProfile>();

                if (calenderVm.InvitedUsers.Any() && calenderVm.InvitedUsers[0] != null)
                {
                    var invites = calenderVm.InvitedUsers.Select(x => new UserIdCalenderId()
                    {
                        UserId = x,
                        CalenderId = meeting.Id.ToString()
                    });

                    //Add meetingOwner to the meeting
                    invites.ToList().Add(new UserIdCalenderId() { UserId = userId, CalenderId = meeting.MeetingId });
                    Db.UserIdMeetingIds.AddRange(invites);

                    foreach (var invitedUser in calenderVm.InvitedUsers)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                        if (user is null)
                            throw new RecordNotFoundException("Meeting created but no member was added to the meeting. Kindly add valid members to the meeting as the member supplied does not exist");

                        invitedUsers.Add(user);
                        invitedMmemberEmails.Add(user.Email);
                        invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                        attendees.Add(user.Email, user.UserId);

                        if (invitedUser == userId) continue; // Skip the meeting owner as they are already added

                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been invited to a meeting - {meeting.Name} by {invitee}",
                            Event = EventCategory.Calender,
                            EventId = meeting.Id.ToString(),
                            CreatedBy = userId,
                        };
                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                        {
                            await AddUserNotification(new List<string> { invitedUser }, Guid.Parse(notificationId));
                        }

                        await _hubContext.Clients.All.RecieveNotification();
                    }
                    result = await Db.SaveChangesAsync();
                }

                if (result > 0)
                {
                    if (calenderVm.ExternalTeamMemberEmails.Any() && calenderVm.ExternalTeamMemberEmails[0] != null)
                    {
                        calenderVm.ExternalTeamMemberEmails.ForEach(x =>
                        {
                            if (!attendees.ContainsKey(x)) attendees.Add(x, null);
                        });
                    }

                    // Send an iCalender file to the invitees and the meeting owner
                    var env = new CalenderEventDto
                    {
                        Title = meeting.Name,
                        Description = meeting.Name,
                        Location = meeting.Location,
                        StartDateAndTime = meeting.StartDate,
                        EndDateAndTime = meeting.EndTime.Value,
                        Attendees = attendees,
                        Organizer = inviteeDetails,
                        MeetingId = meeting.Id.ToString(),
                        MeetingLink = meeting.MeetingLink,
                        subdomain = calenderVm.SubDomain,
                        Frequency = calenderVm.Frequency,
                        CustomFrequencyDto = calenderVm.Frequency != null ? null : calenderVm.CustomFrequency,
                    };

                    // Add the organizer to the attendees
                    if (!env.Attendees.ContainsKey(env.Organizer.Email))
                        env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);

                    var icalenderStream = CalenderHelper.CreateCalenderEvent(env);

                    //foreach (var email in inviteeEmails.Distinct().ToList())
                    //{
                    //    env.Email = email;
                    //    CalenderHelper.CreateCalenderEvent(env);
                    //    //BackgroundJob.Enqueue(() => CalenderHelper.CreateCalenderEvent(env));
                    //}

                    // Calculate and update the subsequent meeting dates table
                    var (aiName, aiImage) = await GetAiDetails(calenderVm.Token);

                    var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
                    var templateForSubsequentMeetings = string.Empty;
                    invitedMmemberNames.AddRange(calenderVm.ExternalTeamMemberEmails);
                    var message = string.Format("{0} has invited you to a meeting.", invitee);
                    var title = string.Format(" Meeting Invitation - {0}", calenderVm.Name);
                    if (invitedMmemberEmails.Any())
                    {
                        inviteeEmails.AddRange(invitedMmemberEmails);
                        if (meeting.NotifyMe == NotifyMeVia.Email || meeting.NotifyMe == NotifyMeVia.Both)
                        {
                            SendMailToInternalMembers(calenderVm.SubDomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, invitedUsers, false, title, message, aiName, aiImage, fileURLs, true, icalenderStream);
                        }
                    }

                    // Send mail to external team member
                    if (calenderVm.ExternalTeamMemberEmails.Any() && calenderVm.ExternalTeamMemberEmails[0] != null)
                    {
                        inviteeEmails.AddRange(calenderVm.ExternalTeamMemberEmails);
                        var externalTeamMembers = calenderVm.ExternalTeamMemberEmails.Select(x => new UserIdCalenderId()
                        {
                            CalenderId = meeting.Id.ToString(),
                            Email = x
                        });

                        Db.UserIdMeetingIds.AddRange(externalTeamMembers);
                        result = await Db.SaveChangesAsync();

                        if (meeting.NotifyMe == NotifyMeVia.Email || meeting.NotifyMe == NotifyMeVia.Both)
                        {
                            SendMailToExternalMembers(calenderVm.SubDomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, false, calenderVm.ExternalTeamMemberEmails, "Meeting Invitation", message, aiName, aiImage, invitee, fileURLs, true, icalenderStream);
                        }
                    }
                    var recurrentEmail = new RecurrentMeetingEmailDto()
                    {
                        AiImage = aiImage,
                        AiName = aiName,
                        FileUrls = fileURLs,
                        Title = title,
                        Message = message,
                        InvitedMemberEmails = invitedMmemberEmails,
                        InvitedMemberNames = invitedMmemberNames,
                        InvitedUsers = invitedUsers,
                        Invitee = invitee,
                        InviteeEmail = inviteeEmail,
                        Template = templateFromFolder
                    };
                    await _backGroundService.AddMeetingSubsequentDates(calenderVm, meeting);
                }
                else
                    throw new Exception("Failed to add members to the meeting");
            }
            else
                throw new Exception("Failed to create meeting");

            // Delete records from redis cache for assigned internal users
            calenderVm.InvitedUsers.Add(userId);
            await DeleteDataFromRedis(calenderVm.InvitedUsers);

            _logService.LogTypeResponse(calenderVm, meeting, nameof(CreateMeeting), "Create Calender response");
            return await GetMeetingById(meeting.Id.ToString());
        }
        #endregion

        #region Update or Reschedule Meeting
        /// <summary>
        /// Update meeting or event
        /// </summary>
        /// <param name="updateCalenderDto"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<CalenderMeeting> UpdateMeetingOrEvents(UpdateCalenderDto updateCalenderDto)
        {
            if (updateCalenderDto.AIResheduleForMeetingsThatDidntHappen)
            {
                Db = new JobProDbContext(_conString, new DbContextSchema(updateCalenderDto.SubDomain));
            }

            // Get the invitee name
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == updateCalenderDto.UserId)
                .FirstOrDefault();
            if (inviteeDetails == null)
                throw new RecordNotFoundException("User not found");

            if (!updateCalenderDto.AIResheduleForMeetingsThatDidntHappen)
            {
                var hasPermission = await CheckUserPermission(updateCalenderDto.UserId, Permissions.can_create_meetings);
                if (!hasPermission)
                    throw new UnauthorizedAccessException("You do not have the permission to update or reshedule this meeting");
            }

            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
            var inviteeEmail = inviteeDetails.Email;

            // Check if a meeting or event exist with the supplied calender id
            var calender = Db.CalenderMeetings.Where(x => x.Id == updateCalenderDto.Id).FirstOrDefault();
            if (calender == null) { throw new RecordNotFoundException("Meeting or event does not exist"); }

            if (calender.IsCancelled)
                throw new InvalidOperationException("Update is not allowed. Meeting has been cancelled");

            // Check for permission
            await CheckPermission(calender.CreatedBy.ToString(), updateCalenderDto.UserId);

            // Check if the meeting or event is a recurring meeting or event
            if (updateCalenderDto.HasCustomFrequency)
            {
                CustomFrequencyValidations(updateCalenderDto.CustomFrequency, updateCalenderDto.StartDate);
                var customFrequency = Db.CustomFrequency.Where(x => x.CalenderMeetingId == calender.Id).FirstOrDefault();
                if (customFrequency == null) { throw new RecordNotFoundException("Custom frequency does not exist"); }

                // Update the custom frequency
                // customFrequency = _mapper.Map<CustomFrequency>(updateCalenderDto.CustomFrequency);
                customFrequency.EndsAfter = updateCalenderDto.CustomFrequency.EndsAfter;
                customFrequency.RepeatEvery = updateCalenderDto.CustomFrequency.RepeatEvery;
                customFrequency.RepeatOn = updateCalenderDto.CustomFrequency.RepeatOn;
                customFrequency.RepeatCount = updateCalenderDto.CustomFrequency.RepeatCount;
                customFrequency.EndStatus = updateCalenderDto.CustomFrequency.EndStatus;
                customFrequency.EndsOn = updateCalenderDto.CustomFrequency.EndsOn;
                Db.CustomFrequency.Update(customFrequency);
            }

            // Update the meeting or event
            int duration = Convert.ToInt32(updateCalenderDto.EndTime.TimeOfDay.TotalMinutes - updateCalenderDto.StartDate.TimeOfDay.TotalMinutes);
            if (duration / 60 == 0)
                updateCalenderDto.MeetingDuration = duration.ToString() + " mins";
            else
                updateCalenderDto.MeetingDuration = (duration / 60).ToString() + "hrs " + (duration % 60).ToString() + "mins";

            calender.Name = updateCalenderDto.Name;
            calender.StartDate = updateCalenderDto.StartDate;
            calender.EndDate = updateCalenderDto.EndDate;
            calender.EndTime = updateCalenderDto.EndTime;
            calender.MeetingDuration = updateCalenderDto.MeetingDuration;
            calender.Frequency = updateCalenderDto.Frequency;
            calender.HasCustomFrequency = updateCalenderDto.HasCustomFrequency;
            calender.NotifyMe = updateCalenderDto.NotifyMe;
            calender.Location = updateCalenderDto.Location;
            calender.MakeSchdulePrivate = updateCalenderDto.MakeSchdulePrivate;
            calender.NotifyMembersIn = updateCalenderDto.NotifyMeInMinutes;
            calender.RescheduleCount = updateCalenderDto.RescheduleCount;
            calender.MeetLength = duration;
            Db.CalenderMeetings.Update(calender);

            //Convert Base64 to iFormFile and upload
            var fileURLs = new List<string>();
            if (updateCalenderDto?.AttachmentBase64 != null && updateCalenderDto.AttachmentBase64.Any())
            {
                var attachments = new List<IFormFile>();

                foreach (var attachment in updateCalenderDto.AttachmentBase64)
                {
                    var file = Utility.ConvertBase64ToFile(attachment);
                    if (file != null)
                    {
                        attachments.Add(file);
                    }
                }

                // Upload the IFormFile using initial Upload Document Service
                var uploads = await UploadDocument(new UploadDocumentDto
                {
                    MeetingId = calender.Id.ToString(),
                    Files = attachments
                });

                // Get Signed URL for each uploaded file to be used in email notification
                foreach (var file in uploads)
                {
                    var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(file);
                    fileURLs.Add(signedUrl);
                }
            }

            // Delete meeting members
            var userMeetingIds = Db.UserIdMeetingIds.Where(x => x.CalenderId == calender.Id.ToString()).ToList();
            if (userMeetingIds.Count > 0)
                Db.UserIdMeetingIds.RemoveRange(userMeetingIds);

            // Delete subsequent meeting dates from subsequentmeetingdates table
            var subsequentMeetingDates = Db.SubsequentMeetings.Where(x => x.CalenderMeetingId == calender.Id).ToList();
            if (subsequentMeetingDates.Count > 0)
                Db.SubsequentMeetings.RemoveRange(subsequentMeetingDates);

            // Add the new user meeting ids
            var userMeetingIdsToAdd = new List<UserIdCalenderId>();
            foreach (var item in updateCalenderDto.InvitedUsers)
            {
                userMeetingIdsToAdd.Add(new UserIdCalenderId
                {
                    CalenderId = calender.Id.ToString(),
                    UserId = item
                });
            }

            // Add new external team member to database
            var externalTeamMembers = updateCalenderDto.ExternalTeamMemberEmails.Select(x => new UserIdCalenderId()
            {
                CalenderId = calender.Id.ToString(),
                Email = x
            });

            await Db.UserIdMeetingIds.AddRangeAsync(userMeetingIdsToAdd);
            await Db.UserIdMeetingIds.AddRangeAsync(externalTeamMembers);
            var result = await Db.SaveChangesAsync();

            var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
            var templateForSubsequentMeetings = string.Empty;

            // var templateFromFolder = File.ReadAllText(templatePath);

            var title = $"Meeting Invitation Update: {updateCalenderDto.Name}";
            var inviteeEmails = new List<string>() { inviteeDetails.Email };

            if (result > 0)
            {
                // Calculate and update the subsequent meeting dates table
                if (updateCalenderDto.ReoccuringDeleteOptions != ReoccuringDeleteOptions.ThisOnly)
                {
                    await _backGroundService.AddMeetingSubsequentDates(new CalenderVm
                    {
                        Frequency = updateCalenderDto.Frequency,
                        CustomFrequency = updateCalenderDto.CustomFrequency,
                        SubDomain = updateCalenderDto.SubDomain,
                        ReoccuringDeleteOptions = updateCalenderDto.ReoccuringDeleteOptions,
                    }, calender);
                }

                var invitedMmemberEmails = new List<string>();
                var invitedMmemberNames = new List<string>();
                var invitedUsers = new List<UserProfile>();
                var attendees = new Dictionary<string, string>();
                var message = string.Format("{0} has invited you to a meeting.", invitee);
                if (updateCalenderDto.InvitedUsers.Any())
                {
                    foreach (var invitedUser in updateCalenderDto.InvitedUsers)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                        if (user is null) continue;
                        invitedUsers.Add(user);
                        invitedMmemberEmails.Add(user?.Email);
                        invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                        if (!attendees.ContainsKey(user.Email))
                            attendees.Add(user.Email, user.UserId);

                        if (invitedUser == updateCalenderDto.UserId) continue;

                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been invited to a meeting - {calender.Name} by {invitee}",
                            Event = EventCategory.Calender,
                            EventId = calender.Id.ToString(),
                            CreatedBy = updateCalenderDto.UserId,
                        };
                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                        {
                            await AddUserNotification(new List<string> { invitedUser }, Guid.Parse(notificationId));
                        }

                        await _hubContext.Clients.All.RecieveNotification();
                        await Db.SaveChangesAsync();
                    }

                    inviteeEmails.AddRange(invitedMmemberEmails);
                }

                // Send an iCalender file to the invitees and the meeting owner
                var env = new CalenderEventDto
                {
                    Title = calender.Name,
                    Description = calender.Name,
                    Location = calender.Location,
                    StartDateAndTime = calender.StartDate,
                    EndDateAndTime = calender.EndTime.Value,
                    Attendees = attendees,
                    Organizer = inviteeDetails,
                    MeetingId = calender.Id.ToString(),
                    MeetingLink = calender.MeetingLink,
                    subdomain = updateCalenderDto.SubDomain,
                    Frequency = updateCalenderDto.Frequency,
                    CustomFrequencyDto = updateCalenderDto.Frequency != null ? null : updateCalenderDto.CustomFrequency,
                };

                // Add the organizer to the attendees
                env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);
                var icalenderStream = CalenderHelper.UpdateCalenderEvent(env);

                var (aiName, aiImage) = await GetAiDetails(updateCalenderDto.Token);
                if (calender.NotifyMe == NotifyMeVia.Email || calender.NotifyMe == NotifyMeVia.Both)
                {
                    SendMailToInternalMembers(updateCalenderDto.SubDomain, invitee, inviteeEmail, calender, templateFromFolder, invitedMmemberNames, invitedUsers, true, title, message, aiName, aiImage, fileURLs, false, icalenderStream);
                }

                // Send mail to external team member
                if (updateCalenderDto.ExternalTeamMemberEmails.Any())
                {
                    updateCalenderDto.ExternalTeamMemberEmails.ForEach(x =>
                    {
                        if (!attendees.ContainsKey(x))
                            attendees.Add(x, null);
                    });

                    inviteeEmails.AddRange(updateCalenderDto.ExternalTeamMemberEmails);
                    invitedMmemberNames.AddRange(updateCalenderDto.ExternalTeamMemberEmails);
                    SendMailToExternalMembers(updateCalenderDto.SubDomain, invitee, inviteeEmail, calender, templateFromFolder, invitedMmemberNames, true, updateCalenderDto.ExternalTeamMemberEmails, title, message, aiName, aiImage, invitee, fileURLs, true, icalenderStream);
                }

                // Delete records from redis cache for assigned internal users
                updateCalenderDto.InvitedUsers.Add(updateCalenderDto.UserId);
                await DeleteDataFromRedis(updateCalenderDto.InvitedUsers);

                return await GetMeetingById(calender.Id.ToString());
            }

            return null;
        }
        #endregion

        #region Upload Document
        /// <summary>
        /// Upload a document to the cloud(gcp)
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<List<string>> UploadDocument(UploadDocumentDto asset)
        {
            if (string.IsNullOrEmpty(asset.MeetingId) && string.IsNullOrEmpty(asset.SubsequentMeetingId))
                throw new DirtyFormException("Meeting id or subsequent meeting id is required");

            if (asset.Files is null || asset.Files.Count == 0)
                throw new DirtyFormException("Files are required for this operation");

            CalenderMeeting meeting = null;
            SubsequentMeeting subsequentMeeting = null;
            var subsequentMeetingIds = new List<Guid>();
            if (!string.IsNullOrEmpty(asset.MeetingId))
            {
                meeting = await Db.CalenderMeetings
                    .FirstOrDefaultAsync(meeting => meeting.Id.ToString() == asset.MeetingId);
                if (meeting is null)
                    throw new RecordNotFoundException("Meeting does not exist");

                // Check if meeting is a recurring meeting
                if (meeting.Frequency != MeetingFrequency.OneOff.ToString())
                {
                    if (asset.ReoccuringDeleteOptions == null)
                        throw new DirtyFormException("Reoccuring options is required for this kind of meeting");
                    if (asset.ReoccuringDeleteOptions == ReoccuringDeleteOptions.ThisAndFuture)
                    {
                        subsequentMeetingIds = await Db.SubsequentMeetings
                            .Where(x => x.CalenderMeetingId == meeting.Id && x.SubsequentMeetingDateTime.Date >= DateTime.UtcNow.Date)
                            .Select(x => x.Id).ToListAsync();
                    }
                    else if (asset.ReoccuringDeleteOptions == ReoccuringDeleteOptions.All)
                    {
                        subsequentMeetingIds = await Db.SubsequentMeetings
                            .Where(x => x.CalenderMeetingId == meeting.Id)
                            .Select(x => x.Id).ToListAsync();
                    }
                }
            }
            else
            {
                subsequentMeeting = await Db.SubsequentMeetings.FirstOrDefaultAsync(x => x.Id.ToString() == asset.SubsequentMeetingId);
                if (subsequentMeeting == null)
                    throw new RecordNotFoundException("Meeting does not exist");
            }

            var fileNames = new List<string>();
            var calenderUpdates = new List<CalenderUpload>();
            foreach (var file in asset.Files)
            {
                Guid guid = Guid.NewGuid();
                var fileTrimmed = file.FileName.Replace(" ", "");
                var fileName = guid.ToString()
                                        .Replace('-', '0')
                                        .Replace('_', '0')
                                        .ToUpper() + "-" + fileTrimmed;

                var imageUrl = await _aWSS3Sevices.UploadFileWithLimitAsync(file, fileName, 50);
                if (imageUrl is not null)
                {
                    fileNames.Add(fileName);
                    if (!string.IsNullOrEmpty(asset.MeetingId))
                    {
                        calenderUpdates.Add(new CalenderUpload
                        {
                            FileName = fileName,
                            MeetingId = meeting.Id
                        });

                        if (subsequentMeetingIds.Any())
                        {
                            foreach (var id in subsequentMeetingIds)
                            {
                                calenderUpdates.Add(new CalenderUpload
                                {
                                    FileName = fileName,
                                    SubsequentMeetingId = id
                                });
                            }
                        }
                    }
                    else
                    {
                        calenderUpdates.Add(new CalenderUpload
                        {
                            FileName = fileName,
                            SubsequentMeetingId = subsequentMeeting.Id
                        });
                    }
                }
            }

            if (calenderUpdates.Any())
            {
                await Db.CalenderUploads.AddRangeAsync(calenderUpdates);
                await Db.SaveChangesAsync();
            }

            return fileNames;
        }
        #endregion

        #region Meeting Recording Notification
        /// <summary>
        /// Upload a document to the cloud(gcp)
        /// </summary>
        /// <param name="recordingNotificationDto"></param>
        /// <returns></returns>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<string> MeetingRecordingNotification(MeetingRecordingNotificationDto recordingNotificationDto)
        {
            // Get subdomain from Tenant table
            var subdomain = recordingNotificationDto.Subdomain;
            if (subdomain is null)
                throw new RecordNotFoundException("Tenant not found");

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var meeting = await context.CalenderMeetings.Where(x => x.MeetingId == recordingNotificationDto.MeetingId && x.IsCancelled == false).FirstOrDefaultAsync();
            if (meeting == null)
            {
                return "Meeting not found";
            }

            var members = await context.UserIdMeetingIds.Where(x => x.CalenderId == meeting.Id.ToString()).ToListAsync();

            // Add meeting owner to the members
            members.Add(new UserIdCalenderId
            {
                CalenderId = meeting.Id.ToString(),
                UserId = meeting.CreatedBy.ToString()
            });

            var invitee = context.UserProfiles.Where(x => x.UserId == meeting.CreatedBy.ToString())
                .Select(x => x.FirstName).FirstOrDefault();
            if (recordingNotificationDto.IsRecordingAvalable)
            {
                var meetingRecording = new CalenderMeetingRecording
                {
                    Id = Guid.NewGuid(),
                    MeetingId = recordingNotificationDto.MeetingId,
                    MeetingRecordingUrl = recordingNotificationDto.MettingRecordingUrl
                };

                await context.CalenderMeetingRecordings.AddAsync(meetingRecording);
                await context.SaveChangesAsync();
                var shortUrl = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/suite/pkg/calendar/recording-link/{1}", subdomain, meetingRecording.MeetingId);
                var subject = $"Meeting Recording: {meeting.Name}";
                var templateFromFolder = ReadTemplateFromFile("processed_link", _environment);
                templateFromFolder = templateFromFolder.Replace("{meetingname}", meeting.Name).Replace("{meetingenddate}", meeting.EndDate?.ToString("yyyyMMdd"))
                                  .Replace("{videolink}", recordingNotificationDto.MettingRecordingUrl).Replace("{invitee}", invitee).Replace("{shortUrl}", shortUrl);
                foreach (var member in members)
                {
                    if (string.IsNullOrEmpty(member.Email))
                    {
                        var memberDetails = await context.UserProfiles.Where(x => x.UserId == member.UserId).FirstOrDefaultAsync();
                        member.Email = memberDetails.Email;
                    }

                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(templateFromFolder, member.Email, subject));
                }

                return "Meeting Recording Sent Successflly";
            }
            else
            {
                var subject = $"Meeting Recording Processing: {meeting.Name}";
                var templateFromFolder = ReadTemplateFromFile("processing_recording", _environment);
                templateFromFolder = templateFromFolder.Replace("{meetingname}", meeting.Name).Replace("{meetingenddate}", meeting.EndDate?.ToString("yyyyMMdd")).Replace("meetingendtime", meeting.EndTime?.ToShortTimeString()).Replace("{invitee}", invitee);
                foreach (var member in members)
                {
                    if (string.IsNullOrEmpty(member.Email) && !string.IsNullOrEmpty(member.UserId))
                    {
                        var memberDetails = await context.UserProfiles.Where(x => x.UserId == member.UserId).FirstOrDefaultAsync();
                        member.Email = memberDetails.Email;
                    }

                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(templateFromFolder, member.Email, subject));
                }
                return "Meeting Members Notified on Successflly";
            }
        }
        #endregion

        #region Get Meeting Recording Url
        /// <summary>
        /// Upload a document to the cloud(gcp)
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<GenericResponse> GetMeetingRecordingUrl(string subdomain, string meetingId)
        {
            // Get subdomain from Tenant table
            if (subdomain is null)
                throw new RecordNotFoundException("Tenant not found");

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var meeting = await context.CalenderMeetingRecordings.Where(x => x.MeetingId == meetingId).FirstOrDefaultAsync();
            if (meeting == null)
            {
                throw new RecordNotFoundException("Meeting recording does not exist");
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = $"Meeting Recording Link",
                Data = meeting.MeetingRecordingUrl
            };

        }
        #endregion

        #region Get uploaded document
        /// <summary>
        /// Get uploaded document
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="subsequentMeetingId"></param>
        /// <returns></returns>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<GenericResponse> GetUploadedDocument(string meetingId, string subsequentMeetingId)
        {
            if (string.IsNullOrEmpty(meetingId) && string.IsNullOrEmpty(subsequentMeetingId))
                return new GenericResponse
                {
                    ResponseCode = "400",
                    DevResponseMessage = "Meeting id or subsequent meeting id is required",
                    ResponseMessage = Utility.Constants.BAD_REQUEST
                };

            List<CalenderUpload> meetingUploads = null;
            if (!string.IsNullOrEmpty(meetingId))
            {
                var meeting = await Db.CalenderMeetings
                    .FirstOrDefaultAsync(meeting => meeting.Id.ToString() == meetingId);
                if (meeting is null)
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        DevResponseMessage = "Meeting does not exist",
                        ResponseMessage = Utility.Constants.BAD_REQUEST
                    };

                meetingUploads = await Db.CalenderUploads
                    .Where(upload => upload.MeetingId == meeting.Id).ToListAsync();
            }
            else
            {
                var subsequentMeeting = await Db.SubsequentMeetings
                    .FirstOrDefaultAsync(x => x.Id.ToString() == subsequentMeetingId);
                if (subsequentMeeting == null)
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        DevResponseMessage = "Meeting does not exist",
                        ResponseMessage = Utility.Constants.BAD_REQUEST
                    };

                meetingUploads = await Db.CalenderUploads
                    .Where(upload => upload.SubsequentMeetingId.ToString() == subsequentMeetingId).ToListAsync();
            }

            var uploads = meetingUploads
                .Select(upload => new CalenderUploadDto
                {
                    FileName = upload.FileName.Split("-").Last(),
                    MeetingId = upload.MeetingId.ToString(),
                    SignedUrl = _aWSS3Sevices.GetSignedUrlAsync(upload.FileName).Result,
                    FullFileName = upload.FileName
                }).ToList();

            var doc = uploads.Count > 1 ? "documents" : "document";
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = $"Uploaded {doc} retrieved successfully",
                Data = uploads
            };
        }
        #endregion

        #region Delete uploaded document
        /// <summary>
        /// Delete uploaded document
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="fileName"></param>
        /// <param name="subsequentMeetingId"></param>
        /// <param name="reoccuringDeleteOptions"></param>
        /// <returns></returns>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<GenericResponse> DeleteUploadedDocument(string meetingId, string fileName, string subsequentMeetingId, ReoccuringDeleteOptions? reoccuringDeleteOptions)
        {
            CalenderMeeting meeting = null;
            SubsequentMeeting subsequentMeeting = null;
            CalenderUpload meetingUpload = null;
            var subsequentMeetingIds = new List<Guid>();
            if (!string.IsNullOrEmpty(meetingId))
            {
                meeting = await Db.CalenderMeetings
                    .FirstOrDefaultAsync(meeting => meeting.Id.ToString() == meetingId);
                if (meeting is null)
                    throw new RecordNotFoundException("Meeting does not exist");

                meetingUpload = await Db.CalenderUploads
                    .Where(upload => upload.MeetingId == meeting.Id && upload.FileName.ToLower() == fileName.ToLower()).FirstOrDefaultAsync();

                // Check if meeting is a recurring meeting
                if (meeting.Frequency != MeetingFrequency.OneOff.ToString())
                {
                    if (reoccuringDeleteOptions == null)
                        throw new DirtyFormException("Reoccuring delete options is required for this kind of meeting");

                    if (reoccuringDeleteOptions == ReoccuringDeleteOptions.ThisAndFuture)
                    {
                        subsequentMeetingIds = await Db.SubsequentMeetings
                            .Where(x => x.CalenderMeetingId == meeting.Id && x.SubsequentMeetingDateTime.Date >= DateTime.UtcNow.Date)
                            .Select(x => x.Id).ToListAsync();
                    }
                    else if (reoccuringDeleteOptions == ReoccuringDeleteOptions.All)
                    {
                        subsequentMeetingIds = await Db.SubsequentMeetings
                            .Where(x => x.CalenderMeetingId == meeting.Id)
                            .Select(x => x.Id).ToListAsync();

                        await _aWSS3Sevices.DeleteFileAsync(meetingUpload.FileName);
                    }
                }

                if (subsequentMeetingIds.Any())
                {
                    var meetingUploads = await Db.CalenderUploads
                        .Where(upload => subsequentMeetingIds.Contains(upload.SubsequentMeetingId.Value)).ToListAsync();
                    if (meetingUploads != null)
                        Db.CalenderUploads.RemoveRange(meetingUploads);
                }
            }
            else
            {
                subsequentMeeting = await Db.SubsequentMeetings.FirstOrDefaultAsync(x => x.Id.ToString() == subsequentMeetingId);
                if (subsequentMeeting == null)
                    throw new RecordNotFoundException("Meeting does not exist");

                meetingUpload = await Db.CalenderUploads
                    .Where(upload => upload.SubsequentMeetingId.ToString() == subsequentMeetingId && upload.FileName.ToLower() == fileName.ToLower()).FirstOrDefaultAsync();
            }

            Db.CalenderUploads.Remove(meetingUpload);
            var dbResult = await Db.SaveChangesAsync();

            // Delete records from redis cache for assigned internal users
            var meetingMemberIds = await Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId)
                .Select(x => x.UserId).ToListAsync();
            meetingMemberIds.Add(meeting.CreatedBy.ToString());
            await DeleteDataFromRedis(meetingMemberIds);

            return new GenericResponse
            {
                ResponseCode = dbResult > 0 ? "200" : "400",
                ResponseMessage = dbResult > 0 ? "Deleted files successfully" : "Failed to delete files",
                Data = null
            };
        }
        #endregion

        #region Propose New Date And Time For A Meeting
        public async Task<GenericResponse> ProposeNewTimeForMeeting(ProposeNewDateAndTimeDto model, string host)
        {
            var meeting = await Db.CalenderMeetings
                .Where(x => x.Id.ToString() == model.MeetingId).FirstOrDefaultAsync();

            // Ensure that the proposed date and time is at least 1 hour from the current date and time
            if (model.NewProposedStartDateAndTime < DateTime.UtcNow.AddHours(1))
                throw new InvalidOperationException("Proposed date and time must be at least 1 hour from the current date and time");

            if (meeting != null)
            {
                // Get meeting owner
                var meetingOwnerId = meeting.CreatedBy;
                var meetingOnwner = await Db.UserProfiles.Where(x => x.UserId == meetingOwnerId.ToString())
                    .FirstOrDefaultAsync();
                if (meetingOnwner.Email == model.RequesterEmail)
                    throw new InvalidOperationException("You cannot propose a new date and time for a meeting you created");

                // Check if the requester email is among the invitees
                //External Members
                var inviteeEmail = await Db.UserIdMeetingIds.Where(x => x.CalenderId == model.MeetingId && x.Email.ToLower() == model.RequesterEmail.ToLower())
                    .Select(x => x.Email).FirstOrDefaultAsync();

                var invitee = await Db.UserProfiles
                     .Where(u => u.Email.ToLower() == model.RequesterEmail.ToLower())
                     .Select(x => new { x.Email, x.FirstName, x.LastName })
                     .FirstOrDefaultAsync();

                if (invitee == null)
                {
                    invitee = await Dbo.Users
                        .Where(u => u.Email.ToLower() == model.RequesterEmail.ToLower())
                        .Select(x => new { x.Email, x.FirstName, x.LastName })
                        .FirstOrDefaultAsync();
                }

                if (invitee == null)
                {
                    throw new RecordNotFoundException("Requester email supplied was not invited to this meeting or it does not exist");
                }

                // Add the record to the database
                var meetingProposedDate = new ProposedDateDetail
                {
                    MeetingId = Guid.Parse(model.MeetingId),
                    NewProposedStartDateAndTime = model.NewProposedStartDateAndTime,
                    NewProposedEndDateAndTime = model.NewProposedEndDateAndTime,
                    Reason = model.Reason,
                    ProposedByEmail = model.RequesterEmail
                };
                Db.ProposedDateDetails.Add(meetingProposedDate);
                var dbResult = await Db.SaveChangesAsync();
                if (dbResult <= 0)
                    throw new InvalidOperationException("Failed to propose new date and time for the meeting");

                inviteeEmail = invitee.Email;

                var (aiName, aiImage) = await GetAiDetails("");

                var acceptUrl = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/replies?MeetingId={1}&newProposedStartDateAndTime={2}&newProposedEndDateAndTime={3}&ownerEmail={4}&meetingOwnerId={5}&requesterEmail={6}&accepted={7}&ResponseType=yes", host, meeting.Id, model.NewProposedStartDateAndTime, model.NewProposedEndDateAndTime, meetingOnwner.Email, meetingOwnerId, model.RequesterEmail, true);

                var rejectUrl = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/replies?MeetingId={1}&newProposedStartDateAndTime={2}&newProposedEndDateAndTime={3}&ownerEmail={4}&meetingOwnerId={5}&requesterEmail={6}&accepted={7}&ResponseType=no", host, meeting.Id, model.NewProposedStartDateAndTime, model.NewProposedEndDateAndTime, meetingOnwner.Email, meetingOwnerId, model.RequesterEmail, false);

                // Todo: Send email to meeting owner
                var templateFromFolder = ReadTemplateFromFile("propose_new_meeting_template_new", _environment);
                var subject = $"New Proposed Date And Time: {meeting.Name}";
                var name = meetingOnwner.FirstName + " " + meetingOnwner.LastName;
                var message = $"{invitee.FirstName ?? inviteeEmail} has proposed a new Date and Time for the meeting due to {model.Reason}";
                int duration = Convert.ToInt32((model.NewProposedEndDateAndTime - model.NewProposedStartDateAndTime).TotalMinutes);

                var meetDuration = duration / 60 == 0 ? duration.ToString() + " mins" : (duration / 60).ToString() + "hrs " + (duration % 60).ToString() + "mins";
                templateFromFolder = templateFromFolder.Replace("{name}", name).Replace("{message}", message).Replace("{meeting name}", meeting.Name).Replace("{location}", meeting.Location)
                    .Replace("{yesurl}", acceptUrl).Replace("{nourl}", rejectUrl).Replace("{maybeurl}", "").Replace("{duration}", meetDuration)
                    .Replace("{link}", meeting.MeetingLink).Replace("{organizer}", name).Replace("{ppnturl}", "").Replace("{invitee}", name).Replace("{more}", "https://joble.app/").Replace("{aiName}", aiName).Replace("{aiImage}", aiImage);


                await _emailService.SendEmail(templateFromFolder, meetingOnwner.Email, subject);
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "New Proposed Date And Time Sent Successfully",
                    Data = null
                };
            }
            else
                throw new RecordNotFoundException("Meeting does not exist");
        }
        #endregion

        #region Accept Or Reject NewProposedDateOrTime
        public async Task<GenericResponse> AcceptOrRejectNewProposedDateOrTime(AcceptOrRejectNewProposedDateAndTimeDto model)
        {
            var meeting = await Db.CalenderMeetings.Include(x => x.CustomFrequency)             //Ask Dozie Why ?
                .Where(x => x.Id.ToString() == model.MeetingId).FirstOrDefaultAsync();
            if (meeting is null)
                throw new RecordNotFoundException("Meeting does not exist");

            // Ensure that the proposed new date and time is at least 30 minutes from the current date and time
            if (model.NewProposedStartDateAndTime < DateTime.UtcNow.AddMinutes(30) && model.Accepted)
                throw new InvalidOperationException("Proposed date and time must be at least 30 minutes from the current date and time");

            // Check for permisions
            await CheckPermission(meeting.CreatedBy.ToString(), model.UserId);
            var meetingMemberIds = await Db.UserIdMeetingIds.Where(x => x.CalenderId == model.MeetingId).ToListAsync();
            var externalMemberEmails = new List<string>();
            var internalMemberIds = new List<string>();
            if (meetingMemberIds.Any())
            {
                foreach (var meetingMember in meetingMemberIds)
                {
                    if (meetingMember?.Email is not null)
                        externalMemberEmails.Add(meetingMember?.Email);
                    if (meetingMember?.UserId is not null)
                        internalMemberIds.Add(meetingMember.UserId);
                }
            }

            var invitedMmemberEmails = new List<string>();
            var invitedMmemberNames = new List<string>();
            var invitedUsers = new List<UserProfile>();
            if (internalMemberIds.Any() && internalMemberIds[0] is not null)
            {
                foreach (var invitedUser in internalMemberIds)
                {
                    var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                    invitedUsers.Add(user);
                    invitedMmemberEmails.Add(user.Email);
                    invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                }
            }

            var inviteeEmails = new List<string>();
            var templateForSubsequentMeetings = string.Empty;
            var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
            invitedMmemberNames.AddRange(externalMemberEmails);

            // Get the record from the db and update accordingly
            var proposedDate = await Db.ProposedDateDetails
                .Where(x => x.MeetingId == meeting.Id && x.NewProposedStartDateAndTime == model.NewProposedStartDateAndTime && model.NewProposedEndDateAndTime == x.NewProposedEndDateAndTime && x.ProposedByEmail == model.RequesterEmail).FirstOrDefaultAsync();
            if (proposedDate is null)
                throw new RecordNotFoundException("Proposed date and time does not exist");

            proposedDate.Status = model.Accepted ? ProposedNewDateStatus.Accepted : ProposedNewDateStatus.Rejected;
            proposedDate.AcceptedOn = DateTime.UtcNow;
            Db.ProposedDateDetails.Update(proposedDate);
            var dbResult = await Db.SaveChangesAsync() > 0;

            if (model.Accepted && dbResult)
            {
                // Get the meeting uploads
                var meetingUploads = await Db.CalenderUploads
                .Where(upload => upload.MeetingId == meeting.Id).ToListAsync();

                var uploads = new List<string>();
                if (meetingUploads.Any())
                {
                    foreach (var upload in meetingUploads)
                    {
                        var res = await _aWSS3Sevices.GetSignedUrlAsync(upload.FileName);
                        uploads.Add(res);
                    }
                }

                // Get the meeting invitees
                meeting.StartDate = model.NewProposedStartDateAndTime;
                meeting.EndDate = model.NewProposedEndDateAndTime;
                Db.CalenderMeetings.Update(meeting);
                var saveResponse = await Db.SaveChangesAsync();

                if (saveResponse > 0)
                {
                    if (meeting.NotifyMe == NotifyMeVia.Email || meeting.NotifyMe == NotifyMeVia.Both)
                    {
                        var inviteeDetails = await Db.UserProfiles.Where(x => x.UserId == meeting.CreatedBy.ToString()).FirstOrDefaultAsync();
                        var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
                        var inviteeEmail = inviteeDetails.Email;

                        // Send an email update to all invited users

                        // Calculate and update the subsequent meeting dates table
                        var calenderVm = new CalenderVm
                        {
                            Frequency = meeting.Frequency,
                            CustomFrequency = meeting.CustomFrequency is not null ? new CustomFrequencyDto
                            {
                                EndStatus = meeting.CustomFrequency.EndStatus,
                                RepeatCount = meeting.CustomFrequency.RepeatCount,
                                RepeatEvery = meeting.CustomFrequency.RepeatEvery,
                                RepeatOn = meeting.CustomFrequency.RepeatOn,
                                EndsAfter = meeting.CustomFrequency.EndsAfter,
                                EndsOn = meeting.CustomFrequency.EndsOn,
                                CalenderMeetingId = meeting.Id
                            } : null,
                            SubDomain = model.Subdomain,
                        };

                        await _backGroundService.AddMeetingSubsequentDates(calenderVm, meeting);
                        var (aiName, aiImage) = await GetAiDetails(model.Token);
                        var message = string.Format("{0} has invited you to a meeting.", invitee);
                        var title = $"Meeting Invitation Update: {meeting.Name}";
                        if (invitedMmemberEmails.Any())
                        {
                            inviteeEmails.AddRange(invitedMmemberEmails);
                            if (meeting.NotifyMe == NotifyMeVia.Email || meeting.NotifyMe == NotifyMeVia.Both)
                            {
                                SendMailToInternalMembers(model.Subdomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, invitedUsers, true, title, message, aiName, aiImage, uploads);
                            }
                        }

                        // Send mail to external team member
                        if (externalMemberEmails.Any())
                        {
                            inviteeEmails.AddRange(externalMemberEmails);
                            SendMailToExternalMembers(model.Subdomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, true, externalMemberEmails, title, message, aiName, aiImage, null, uploads);
                        }
                    }
                }
            }
            else
            {
                // Todo: Send an email to the requester that the request to change date and time was rejected
                var subject = $"Meeting Owner has rejected the proposed date and time for: {meeting.Name}";
                var inviteeDetails = await Db.UserProfiles.Where(x => x.UserId == meeting.CreatedBy.ToString()).FirstOrDefaultAsync();
                var name = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
                var message = $"{name} has rejected proposed new date and time for the meeting";
                var title = $"{meeting.Name}- {name} rejected yor proposed new date and time";
                var userRequestingChange = new List<string>() { model.RequesterEmail };

                SendMailToExternalMembers(model.Subdomain, name, inviteeDetails.Email, meeting, templateFromFolder, invitedMmemberNames, false, userRequestingChange, title, message, Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
            }

            // Delete records from redis cache for assigned internal users
            var meetingMembers = meetingMemberIds.Where(meet => meet.UserId != null).Select(x => x.UserId).ToList();
            //var meetingMembers = await Db.UserIdMeetingIds.Where(meet => meet.Id.ToString() == model.MeetingId && meet.UserId != null)
            //                                    .Select(x => x.UserId).ToListAsync();
            meetingMembers.Add(meeting.CreatedBy.ToString());
            //await DeleteDataFromRedis(meetingMembers);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "New Proposed Date And Time has been processed Successfully",
                Data = null
            };
        }
        #endregion

        #region Process Meeting Response
        /// <summary>
        /// Process Meeting Response
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> ProcessMeetingResponse(MeetingResponseVm model)
        {
            var meeting = await Db.CalenderMeetings
                .Where(x => x.Id.ToString() == model.MeetingId).FirstOrDefaultAsync();
            if (meeting is null)
                throw new RecordNotFoundException("Meeting does not exist");

            // Get the invited users
            var meetingUsers = await Db.UserIdMeetingIds
                .Where(x => x.CalenderId == model.MeetingId).ToListAsync();
            var invitee = await Db.UserProfiles.Where(x => x.UserId == meeting.CreatedBy.ToString()).FirstOrDefaultAsync();

            // If the person that created the meeting clicks yes in the calendar invite
            if (invitee.Email == model.AcceptedUserEmail)
                return true;

            var inviteeName = invitee.FirstName + " " + invitee.LastName;

            //Check if the person accepting the email is a member of the meeting
            var invitedUser = meetingUsers.Where(x => x.Email == model.AcceptedUserEmail).FirstOrDefault();
            var invitedUserName = string.Empty;
            if (invitedUser is null)
            {
                var invitedUserId = await Db.UserProfiles.Where(x => x.Email == model.AcceptedUserEmail).Select(x => x.UserId).FirstOrDefaultAsync();
                invitedUserId ??= await Dbo.Users.Where(x => x.Email == model.AcceptedUserEmail).Select(x => x.Id).FirstOrDefaultAsync();
                invitedUser = meetingUsers.Where(x => x.UserId == invitedUserId).FirstOrDefault();
                if (invitedUser is null)
                    throw new RecordNotFoundException($"{model.AcceptedUserEmail} was not invited to this meeting");

                invitedUserName = await Db.UserProfiles.Where(x => x.UserId == invitedUserId).Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
            }

            if (string.IsNullOrEmpty(invitedUserName))
                invitedUserName = "Dear";

            var message = "";
            var bg = "";
            switch (model.ResponseType)
            {
                case "yes":
                    message = $"{model.AcceptedUser} has Accepted this Invitation";
                    bg = "#cfe8da";
                    invitedUser.InviteResponse = InviteResponse.Yes;
                    break;
                case "no":
                    message = $"{model.AcceptedUser} has Declined this Invitation";
                    bg = "#f6d9d6";
                    invitedUser.InviteResponse = InviteResponse.No;
                    break;
                case "maybe":
                    message = $"{model.AcceptedUser} has replied ‘Maybe” to this invitation and proposed a new time: Tuesday, 22nd December. 3:30-4:30 PM";
                    bg = "#fdead3";
                    invitedUser.InviteResponse = InviteResponse.Maybe;
                    break;
                default:
                    break;
            }

            Db.UserIdMeetingIds.Update(invitedUser);
            await Db.SaveChangesAsync();

            var invitedMmemberNames = new List<string>();
            var invitedMmemberEmails = new List<string>();
            var invitedUsers = new List<UserProfile>();
            if (meetingUsers.Any())
            {
                foreach (var invitedUserDetails in meetingUsers)
                {
                    var user = await Db.UserProfiles.Where(x => x.UserId == invitedUserDetails.UserId).FirstOrDefaultAsync();

                    if (user is not null)
                    {
                        invitedUsers.Add(user);
                        if (user is not null) invitedMmemberEmails.Add(user.Email);
                        invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                    }

                }

                var attachment = await Db.CalenderUploads.Where(x => x.MeetingId == meeting.Id).Select(x => x.FileName).ToListAsync();

                //var template = await _emailService.GetMeetingResponseMailTemplate();
                var (aiName, aiImage) = await GetAiDetails("");

                var title = $"Meeting Response - {meeting.Name}";
                var template = ReadTemplateFromFile("meeting_template_new", _environment);
                template = template.Replace("{name}", invitedUserName).Replace("{message}", message).Replace("{meeting name}", meeting.Name).Replace("{location}", meeting.Location)
                    .Replace("{yesurl}", "").Replace("{nourl}", "").Replace("{maybeurl}", "").Replace("{duration}", meeting.MeetingDuration).Replace("{link}", meeting.MeetingLink).Replace("{meetingId}", meeting.MeetingId)
                    .Replace("{organizer}", inviteeName).Replace("{ppnturl}", "").Replace("{invitee}", inviteeName).Replace("{more}", "https://joble.app/").Replace("{attachmentUrl}", meeting?.Attachment).Replace("{aiName}", aiName).Replace("{aiImage}", aiImage);
                template = template.Replace("{date}", meeting.StartDate.ToShortDateString()).Replace("{time}", meeting.StartDate.ToShortTimeString());
                for (var i = 1; i <= invitedMmemberNames.Count; i++)
                {
                    var tem = "{" + i + "}";
                    template = template.Replace(tem, invitedMmemberNames[i - 1]);
                }

                for (var j = invitedMmemberNames.Count + 1; j <= 20; j++)
                {
                    var tem = "{" + j + "}";
                    template = template.Replace(tem, "");
                }

                for (var i = 1; i <= attachment?.Count; i++)
                {
                    var tem = "{attachmentURL" + i + "}";
                    template = template.Replace(tem, attachment[i - 1]);
                }

                for (var j = attachment?.Count + 1; j <= 5; j++)
                {
                    var tem = "{attachmentVisibility" + j + "}";
                    template = template.Replace(tem, "display: none;");
                }

                BackgroundJob.Enqueue(() => _emailService.SendEmail(template, invitee.Email, title));
                //BackgroundJob.Enqueue(() => _emailService.SendEmail(template, "<EMAIL>", title));
            }
            //Clear single meeting Cache
            var cacheKey = $"Calender_{model.MeetingId}";
            var record = await _redisCacheService.RemoveDataAsync(cacheKey);

            // Delete records from redis cache for assigned internal users
            var meetingMembers = await Db.UserIdMeetingIds.Where(meet => meet.CalenderId == model.MeetingId && meet.UserId != null)
                .Select(x => x.UserId).ToListAsync();
            meetingMembers.Add(meeting.CreatedBy.ToString());
            await DeleteDataFromRedis(meetingMembers);

            return true;
        }
        #endregion

        #region Get Meetings By UserId
        /// <summary>
        /// Gets all the meetings for a user
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<List<CalenderMeeting>> GetCalenderById(GetUserScheduleVm model)
        {
            //Get records from redis cache
            var cacheKey = $"meeting_{model.UserId}_{model.DailyDate}_{model.StartDate}_{model.EndDate}_{model.CreatedByAI}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var records = await _redisCacheService.GetDataAsync<List<CalenderMeeting>>(cacheKey);
            if (records != null && records.Any())
            {
                _logService.LogTypeResponse(model, records, nameof(GetCalenderById), "Get Calender response from redis cache");
                return records;
            }

            var meets = new List<CalenderMeeting>();
            var meeting = new CalenderMeeting();
            var meetingIds = await Db.UserIdMeetingIds.Where(x => x.UserId == model.UserId)
                    .Select(x => x.CalenderId).ToListAsync();

            model.StartDate = model.StartDate ?? new DateTime(2023, 01, 01);
            model.EndDate = model.EndDate ?? new DateTime(4000, 01, 01);

            meets = await Db.CalenderMeetings
                    .Include(x => x.CustomFrequency)
                    .Where(x =>
                        x.CreatedBy.ToString() == model.UserId &&
                        x.ScheduleType == CalenderScheduleType.Meeting &&
                        x.IsCancelled == false && x.CreatedByAI == model.CreatedByAI)
                    .ToListAsync();

            var meetings = await Db.CalenderMeetings.Where(x =>
                            meetingIds.Contains(x.Id.ToString()) &&
                                x.ScheduleType == CalenderScheduleType.Meeting &&
                                x.IsCancelled == false && x.CreatedByAI == model.CreatedByAI)
                            .ToListAsync();
            meets.AddRange(meetings);
            var meetingIdsGeneral = meets.Select(x => x.Id).ToList();
            if (model.DailyDate == null)
            {
                var subsequentMeetings = await Db.SubsequentMeetings
                   .Where(x =>
                        x.SubsequentMeetingDateTime.Date >= model.StartDate.Value.Date &&
                        x.SubsequentMeetingDateTime.Date <= model.EndDate.Value.Date &&
                        meetingIdsGeneral.Contains(x.CalenderMeetingId) &&
                        !x.IsCanceled
                    )
                   .Include(c => c.CalenderMeeting)
                   .ToListAsync();
                var recurringMeetings = subsequentMeetings.Select(x => x.CalenderMeeting).Distinct().ToList();
                foreach (var meet in recurringMeetings)
                {
                    var subMeets = subsequentMeetings.Where(x => x.CalenderMeetingId == meet.Id).ToList();
                    meet.SubsequentMeetingDates = subMeets;
                }
                meets = meets.Where(x =>
                            model.StartDate.Value.Date <= x.StartDate.Date &&
                            model.EndDate.Value.Date >= x.EndTime.Value.Date)
                        .ToList();
                meets.AddRange(recurringMeetings);
            }
            else
            {
                var subsequentMeetings = await Db.SubsequentMeetings
                  .Where(x =>
                       x.SubsequentMeetingDateTime.Date == model.DailyDate.Value.Date &&
                       meetingIdsGeneral.Contains(x.CalenderMeetingId) && !x.IsCanceled
                   )
                  .Include(c => c.CalenderMeeting)
                  .ToListAsync();
                var recurringMeetings = subsequentMeetings.Select(x => x.CalenderMeeting).Distinct().ToList();
                foreach (var meet in recurringMeetings)
                {
                    var subMeets = subsequentMeetings.Where(x => x.CalenderMeetingId == meet.Id).ToList();
                    meet.SubsequentMeetingDates = subMeets;
                };

                meets = meets.Where(x => x.StartDate.Date == model.DailyDate.Value.Date).ToList();
                meets.AddRange(recurringMeetings);
            }
            meets = meets.Distinct().ToList();

            foreach (var meet in meets)
            {
                var attachments = await Db.CalenderUploads.Where(x => x.MeetingId == meet.Id).Select(x => x.FileName).ToListAsync();
                foreach (var file in attachments)
                {
                    meet.AttachmentUrls.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
                };
                var userMeetings = Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).ToList();
                //TODO -- Get Meeting link
                int accepted = 0;
                int rejected = 0;
                int pending = 0;

                Parallel.ForEach(userMeetings, x =>
                {
                    if (x.InviteResponse == InviteResponse.Yes) accepted++;
                    else if (x.InviteResponse == InviteResponse.No) rejected++;
                    else pending++;
                });

                CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
                {
                    Accepted = accepted,
                    Rejected = rejected,
                    Pending = pending
                };

                meet.MeetingResponse = meetResponse;
                var meetingOwner = Db.UserProfiles
                    .Where(x => x.UserId == meet.CreatedBy.ToString())
                    .Select(x => new UserMDVm
                    {
                        FirstName = x.FirstName,
                        LastName = x.LastName,
                        Email = x.Email,
                        ProfilePictureUrl = x.ProfilePictureUrl,
                        Id = x.UserId
                    }).FirstOrDefault();

                if (meetingOwner.ProfilePictureUrl is not null)
                    meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);

                var members = userMeetings.Select(x =>
                {
                    var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                    if (user != null)
                    {
                        return new UserMDVm
                        {
                            Id = user.UserId,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email,
                            PhoneNumber = user.PhoneNumber,
                            ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                        };
                    }

                    return new UserMDVm();
                }).ToList();

                meet.ExternalMembers = userMeetings
                    .Where(x => x.Email != null && x.UserId == null)
                    .Select(x => x.Email).ToList();

                var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meet.MeetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
                if (recordingLink is not null)
                    meet.MeetingRecordingLink = recordingLink;
                meet.Members = members.Where(m => m.Id != null || m.Email != null).ToList();
                meet.MeetingOwner = meetingOwner;

                // Get proposed meeting dates
                meet.ProposedDateDetail = await GetProposedNewMeetingDateDeatils(meet.Id.ToString());
            }

            // Add response to redis cache
            await AddDataToRedis(model, cacheKey, meets);

            return meets;
        }
        #endregion

        #region To display the most recent meeting on the Dashboard By UserId
        /// <summary>
        /// Gets all the meetings for a user
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        public async Task<List<CalenderMeeting>> GetMostRecentCalenderById(GetUserScheduleVm model)
        {
            // Get records from redis cache
            var cacheKey = $"meeting_{model.UserId}_{model.DailyDate}_{model.StartDate}_{model.EndDate}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var records = await _redisCacheService.GetDataAsync<List<CalenderMeeting>>(cacheKey);
            if (records != null)
            {
                _logService.LogTypeResponse(model, records, nameof(GetCalenderById), "Get Calender response from redis cache");
                return records;
            }

            var meets = new List<CalenderMeeting>();
            var meeting = new CalenderMeeting();

            var meetingIds = await Db.UserIdMeetingIds.Where(x => x.UserId == model.UserId)
                                                 .Select(x => x.CalenderId).ToListAsync();

            if (model.StartDate != null && model.EndDate != null)
            {
                meets = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
                                                 .Where(x => x.CreatedBy.ToString() == model.UserId
                                                 && x.ScheduleType == CalenderScheduleType.Meeting
                                                 && x.StartDate >= model.StartDate
                                                 && x.EndTime <= model.EndDate
                                                 && x.IsCancelled == false)
                                                .ToListAsync();


                var meetings = await Db.CalenderMeetings.Where(x => meetingIds.Contains(x.Id.ToString())
                                        && x.ScheduleType == CalenderScheduleType.Meeting
                                        && x.StartDate >= model.StartDate
                                        && x.EndTime <= model.EndDate
                                        && x.IsCancelled == false)
                                        .OrderByDescending(x => x.StartDate)
                                       .ThenBy(x => x.EndTime)
                                       .Take(4).ToListAsync();

                meets.AddRange(meetings);
            }
            else if (model.DailyDate != null)
            {
                meets = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
                                                  .Where(x => x.CreatedBy.ToString() == model.UserId
                                                  && x.ScheduleType == CalenderScheduleType.Meeting
                                                  && x.StartDate.Date == model.DailyDate.Value.Date
                                                  && x.IsCancelled == false)
                                                 .ToListAsync();

                var meetings = await Db.CalenderMeetings.Where(x => meetingIds.Contains(x.Id.ToString())
                                  && x.ScheduleType == CalenderScheduleType.Meeting
                                  && x.StartDate.Date == model.DailyDate.Value.Date
                                  && x.IsCancelled == false)
                                  .OrderByDescending(x => x.StartDate.Date)
                                 .ThenBy(x => x.EndTime)
                                 .Take(4).ToListAsync();

                meets.AddRange(meetings);
            }
            else
            {
                meets = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
                                                  .Where(x => x.CreatedBy.ToString() == model.UserId
                                                   && x.ScheduleType == CalenderScheduleType.Meeting
                                                   && x.IsCancelled == false)
                                                  .ToListAsync();

                var meetings = await Db.CalenderMeetings.Where(x => meetingIds.Contains(x.Id.ToString())
                                                        && x.ScheduleType == CalenderScheduleType.Meeting
                                                        && x.IsCancelled == false)
                                                        .OrderByDescending(x => x.StartDate)
                                                       .ThenBy(x => x.EndTime)
                                                       .Take(4).ToListAsync();

                meets.AddRange(meetings);
            }

            foreach (var meet in meets)
            {
                var userMeetings = Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).ToList();
                int accepted = 0;
                int rejected = 0;
                int pending = 0;
                Parallel.ForEach(userMeetings, x =>
                {
                    if (x.InviteResponse == InviteResponse.Yes) accepted++;
                    else if (x.InviteResponse == InviteResponse.No) rejected++;
                    else pending++;
                });
                CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
                {
                    Accepted = accepted,
                    Rejected = rejected,
                    Pending = pending
                };
                meet.MeetingResponse = meetResponse;
                meet.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId == meet.Id).ToListAsync();
                var members = userMeetings.Select(x =>
                {
                    var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                    if (user != null)
                    {
                        return new UserMDVm
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email,
                            PhoneNumber = user.PhoneNumber,
                        };
                    }

                    return new UserMDVm();
                }).ToList();
                var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meet.MeetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
                if (recordingLink is not null)
                    meet.MeetingRecordingLink = recordingLink;
                meet.Members = members.Where(m => m.Id != null || m.Email != null).ToList();
            }

            // Add response to redis cache
            await AddDataToRedis(model, cacheKey, meets);

            return meets;
        }

        #endregion

        #region Get All Meetings Or Meetings for querried users
        public async Task<GenericResponse> GetAllMeetings(GetUsersMeetingVm model)
        {
            if (model.DailyDate == null && (model.StartDate == null || model.EndDate == null))
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide a date range or a daily date"
                };

            var meetsDict = new Dictionary<string, List<CalenderMeeting>>();
            var meets = new List<CalenderMeeting>();
            var meeting = new CalenderMeeting();
            if (model.UserIds.Any() && !string.IsNullOrEmpty(model.UserIds[0]))
            {
                foreach (var userId in model.UserIds)
                {
                    var meetingIds = await Db.UserIdMeetingIds.Where(x => x.UserId == userId)
                            .Select(x => x.CalenderId).ToListAsync();

                    // Add meetingIds of meetings that the user created
                    var userMeetings = await Db.CalenderMeetings.Where(x => x.CreatedBy.ToString() == userId).Select(x => x.Id.ToString()).ToListAsync();
                    meetingIds.AddRange(userMeetings);
                    meetingIds = meetingIds.Distinct().ToList();

                    if (model.StartDate != null && model.EndDate != null)
                    {
                        meets = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
                                            .Where(x => x.CreatedBy.ToString() == userId && x.ScheduleType == CalenderScheduleType.Meeting && x.StartDate.Date >= model.StartDate.Value.Date && x.EndTime.Value.Date <= model.EndDate.Value.Date && x.IsCancelled == false)
                                            .ToListAsync();

                        // Also get meetingIds from subsequent meetings that is within the date duration
                        var subsequentMeetings = await Db.SubsequentMeetings
                            .Where(x => x.SubsequentMeetingDateTime.Date >= model.StartDate.Value.Date && x.SubsequentMeetingDateTime.Date <= model.EndDate.Value.Date && meetingIds.Contains(x.CalenderMeetingId.ToString()) && !x.IsCanceled)
                            .ToListAsync();
                        foreach (var subsequentMeeting in subsequentMeetings)
                        {
                            meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id == subsequentMeeting.CalenderMeetingId && x.IsCancelled == false);
                            if (meeting != null)
                            {
                                meets.Add(meeting);
                            }
                        }

                        foreach (var meetingId in meetingIds)
                        {
                            meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id.ToString() == meetingId && x.ScheduleType == CalenderScheduleType.Meeting && x.StartDate.Date >= model.StartDate.Value.Date && x.EndTime.Value.Date <= model.EndDate.Value.Date && x.IsCancelled == false);

                            if (meeting != null) { meets.Add(meeting); }
                        }
                    }
                    else if (model.DailyDate != null)
                    {
                        meets = await Db.CalenderMeetings
                            .Include(x => x.CustomFrequency)
                            .Where(x => x.CreatedBy.ToString() == userId && x.ScheduleType == CalenderScheduleType.Meeting && x.StartDate.Date == model.DailyDate.Value.Date && x.IsCancelled == false)
                            .ToListAsync();

                        // Also get meetingIds from subsequent meetings that is within the date duration
                        var subsequentMeetings = await Db.SubsequentMeetings
                            .Where(x => x.SubsequentMeetingDateTime.Date == model.DailyDate.Value.Date && meetingIds.Contains(x.CalenderMeetingId.ToString()) && !x.IsCanceled)
                            .ToListAsync();
                        foreach (var subsequentMeeting in subsequentMeetings)
                        {
                            meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id == subsequentMeeting.CalenderMeetingId && x.IsCancelled == false);
                            if (meeting != null)
                            {
                                meets.Add(meeting);
                            }
                        }

                        foreach (var meetingId in meetingIds)
                        {
                            meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id.ToString() == meetingId && x.ScheduleType == CalenderScheduleType.Meeting && x.StartDate.Date == model.DailyDate.Value.Date && x.IsCancelled == false);

                            if (meeting != null) { meets.Add(meeting); }
                        }
                    }
                    else
                    {
                        meets = await Db.CalenderMeetings
                            .Include(x => x.CustomFrequency)
                            .Where(x => x.CreatedBy.ToString() == userId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false)
                            .ToListAsync();

                        foreach (var meetingId in meetingIds)
                        {
                            meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id.ToString() == meetingId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false);

                            if (meeting != null) { meets.Add(meeting); }
                        }
                    }

                    if (meets != null)
                    {
                        foreach (var meet in meets.Distinct())
                        {
                            var attachments = await Db.CalenderUploads.Where(x => x.MeetingId == meet.Id).Select(x => x.FileName).ToListAsync();
                            foreach (var file in attachments)
                            {
                                meet.AttachmentUrls.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
                            };

                            var meetingResponses = await Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).Select(x => x.InviteResponse).ToListAsync();
                            int accepted = 0;
                            int rejected = 0;
                            int pending = 0;
                            Parallel.ForEach(meetingResponses, response =>
                            {
                                if (response == InviteResponse.Yes) accepted++;
                                else if (response == InviteResponse.No) rejected++;
                                else pending++;
                            });
                            CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
                            {
                                Accepted = accepted,
                                Rejected = rejected,
                                Pending = pending
                            };

                            meet.MeetingResponse = meetResponse;

                            var meetingMembers = Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).ToList();
                            var meetingOwner = Db.UserProfiles
                                .Where(x => x.UserId == meet.CreatedBy.ToString())
                                .Select(x => new UserMDVm
                                {
                                    FirstName = x.FirstName,
                                    LastName = x.LastName,
                                    Email = x.Email,
                                    ProfilePictureUrl = x.ProfilePictureUrl,
                                    Id = x.UserId
                                }).FirstOrDefault();

                            if (meetingOwner.ProfilePictureUrl is not null)
                                meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);
                            meet.MeetingOwner = meetingOwner;

                            var members = meetingMembers.Select(x =>
                            {
                                var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                                if (user != null)
                                {
                                    return new UserMDVm
                                    {
                                        Id = user.UserId,
                                        FirstName = user.FirstName,
                                        LastName = user.LastName,
                                        Email = user.Email,
                                        PhoneNumber = user.PhoneNumber,
                                        ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                                    };
                                }

                                return new UserMDVm();
                            }).ToList();
                            meet.Members = members.Where(m => m.Id != null || m.Email != null).ToList();

                            meet.ExternalMembers = meetingMembers
                                .Where(x => x.Email != null && x.UserId == null)
                                .Select(x => x.Email).ToList();

                            if (model.DailyDate != null)
                            {
                                if (model.DailyDate != null)
                                    meet.SubsequentMeetingDates = await Db.SubsequentMeetings
                                        .Where(x => x.SubsequentMeetingDateTime.Date == model.DailyDate.Value.Date && x.CalenderMeetingId == meet.Id)
                                        .ToListAsync();
                            }
                            else if (model.EndDate != null && model.StartDate != null)
                            {
                                meet.SubsequentMeetingDates = await Db.SubsequentMeetings
                                        .Where(x => x.SubsequentMeetingDateTime.Date >= model.StartDate.Value.Date && x.SubsequentMeetingDateTime.Date <= model.EndDate.Value.Date && x.CalenderMeetingId == meet.Id && !x.IsCanceled)
                                        .ToListAsync();
                            }

                            var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meet.MeetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
                            if (recordingLink is not null)
                                meet.MeetingRecordingLink = recordingLink;

                            // Get proposed meeting dates
                            meet.ProposedDateDetail = await GetProposedNewMeetingDateDeatils(meet.Id.ToString());
                        }

                        meetsDict.Add(userId, meets);
                    }
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = meetsDict
            };

        }
        #endregion

        #region Get Events By UserId
        /// <summary>
        ///
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<List<CalenderMeeting>> GetEventsByUserId(GetUserScheduleVm model)
        {
            var userMeetings = await Db.UserIdMeetingIds.Where(x => x.UserId == model.UserId).ToListAsync();

            var meets = new List<CalenderMeeting>();

            foreach (var userMeeting in userMeetings)
            {
                if (model.StartDate != null && model.EndDate != null)
                {
                    var meeting = await Db.CalenderMeetings.Where(x => x.Id.ToString() == userMeeting.CalenderId && x.ScheduleType == CalenderScheduleType.Event && x.StartDate >= model.StartDate && x.EndTime <= model.EndDate).FirstOrDefaultAsync();
                    if (meeting != null) meets.Add(meeting);
                }
                else if (model.DailyDate != null)
                {
                    var meeting = await Db.CalenderMeetings.Where(x => x.Id.ToString() == userMeeting.CalenderId && x.ScheduleType == CalenderScheduleType.Event && x.StartDate.Date == model.DailyDate.Value.Date).FirstOrDefaultAsync();
                    if (meeting != null) meets.Add(meeting);
                }
                else
                {
                    var meeting = await Db.CalenderMeetings.Where(x => x.Id.ToString() == userMeeting.CalenderId && x.ScheduleType == CalenderScheduleType.Event).FirstOrDefaultAsync();
                    if (meeting != null) meets.Add(meeting);
                }
            }

            foreach (var meet in meets)
            {
                var meetingResponses = await Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).Select(x => x.InviteResponse).ToListAsync();
                int accepted = 0;
                int rejected = 0;
                int pending = 0;
                Parallel.ForEach(meetingResponses, response =>
                {
                    if (response == InviteResponse.Yes) accepted++;
                    else if (response == InviteResponse.No) rejected++;
                    else pending++;
                });
                CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
                {
                    Accepted = accepted,
                    Rejected = rejected,
                    Pending = pending
                };
                meet.MeetingResponse = meetResponse;
                meet.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId == meet.Id).ToListAsync();
                var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meet.MeetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
                if (recordingLink is not null)
                    meet.MeetingRecordingLink = recordingLink;
            }

            return meets;
        }
        #endregion

        #region Get users availability
        public async Task<GenericResponse> GetUsersAvailability(GetUserAvailabilityDto model)
        {
            var availabilities = new Dictionary<string, Dictionary<string, List<TimeSpan>>>();
            foreach (var date in model.Dates)
            {
                var availabilitiesPerDate = new Dictionary<string, List<TimeSpan>>();

                var daysMeetings = await Db.CalenderMeetings
                    .Where(c => !c.IsCancelled && c.StartDate.Date == date.Date)
                    .ToListAsync();

                foreach (var userId in model.UserIds)
                {
                    var day = date.DayOfWeek.ToString().ToLower();
                    var personalSchedules = await Db.PersonalSchedule
                        .Where(p => p.UserId.ToString() == userId && p.Available && p.Day.ToLower() == day)
                        .ToListAsync();
                    if (!personalSchedules.Any())
                    {
                        // Add a new schedule for 8 AM to 5 PM as available work hours
                        personalSchedules.Add(new PersonalSchedule
                        {
                            StartTime = DateTime.Today.AddHours(8), // 8 AM
                            EndTime = DateTime.Today.AddHours(17),  // 5 PM
                        });
                    }

                    var userAvailability = new List<TimeSpan>();

                    foreach (var schedule in personalSchedules)
                    {
                        for (var time = schedule.StartTime; time < schedule.EndTime; time = time.Add(TimeSpan.FromMinutes(30)))
                        {
                            if (!daysMeetings.Any())
                            {
                                userAvailability.Add(time.TimeOfDay);
                                continue;
                            }

                            var dayMeetingIds = daysMeetings.Select(x => x.Id.ToString()).ToList();
                            var userCreatedMeetings = daysMeetings.Where(x => x.CreatedBy.ToString() == userId)
                                .Select(u => u.Id.ToString()).ToList();

                            var userParticipatingMeetings = await Db.UserIdMeetingIds
                                .Where(x => dayMeetingIds.Contains(x.CalenderId) && x.UserId == userId)
                                .Select(m => m.CalenderId)
                                .ToListAsync();

                            // Combine both created and participating meeting IDs
                            var userMeetingIds = new List<string>();
                            userMeetingIds.AddRange(userCreatedMeetings);
                            userMeetingIds.AddRange(userParticipatingMeetings);

                            var userMeetings = daysMeetings.Where(x => userMeetingIds.Contains(x.Id.ToString())).ToList();

                            // Check if there's any overlap between the current time slot and the user's meetings
                            var isBusy = userMeetings.Any(meeting =>
                                meeting.StartDate.TimeOfDay <= time.TimeOfDay && time.TimeOfDay < meeting.EndDate.Value.TimeOfDay);

                            if (!isBusy)
                            {
                                userAvailability.Add(time.TimeOfDay);
                            }
                        }
                    }

                    availabilitiesPerDate[userId] = userAvailability;
                }

                availabilities[date.ToString("yyyy-MM-dd")] = availabilitiesPerDate;
            }

            // Return the available time slots for all users
            return new GenericResponse
            {
                ResponseMessage = "Availabilities retrieved successfully",
                Data = availabilities,
                ResponseCode = "200"
            };
        }
        #endregion

        #region Search User Calender To Get Meeting
        public async Task<List<CalenderMeeting>> SearchUserCalenderToGetMeeting(SearchUserCalenderToGetMeetingDto searchUserCalender)
        {
            // Get records from redis cache
            var cacheKey = $"meeting_{searchUserCalender.UserId}_{searchUserCalender.meetingId}_{searchUserCalender.meetingName}_{searchUserCalender.meetingOwner}_{searchUserCalender.meetingMembers}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var records = await _redisCacheService.GetDataAsync<List<CalenderMeeting>>(cacheKey);
            if (records != null)
            {
                _logService.LogTypeResponse(searchUserCalender, records, nameof(GetCalenderById), "Get Calender response from redis cache");
                return records;
            }

            var meets = new List<CalenderMeeting>();
            var meeting = new CalenderMeeting();
            var meetingIds = await Db.UserIdMeetingIds.Where(x => x.UserId == searchUserCalender.UserId)
                    .Select(x => x.CalenderId).ToListAsync();
            var query = Db.CalenderMeetings
                            .Include(x => x.CustomFrequency)
                            .Where(x => x.CreatedBy.ToString() == searchUserCalender.UserId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false);

            if (!string.IsNullOrEmpty(searchUserCalender.meetingId))
            {
                meets = await query.Where(m => m.MeetingId == searchUserCalender.meetingId).ToListAsync();
                foreach (var id in meetingIds)
                {
                    meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id.ToString() == id && x.ScheduleType == CalenderScheduleType.Meeting && x.MeetingId == searchUserCalender.meetingId && x.IsCancelled == false);

                    if (meeting != null) { meets.Add(meeting); }
                }
            }
            if (!string.IsNullOrEmpty(searchUserCalender.meetingName))
            {
                meets = await query.Where(m => m.Name == searchUserCalender.meetingName).ToListAsync();

                foreach (var id in meetingIds)
                {
                    meeting = Db.CalenderMeetings.FirstOrDefault(x => x.Id.ToString() == id && x.ScheduleType == CalenderScheduleType.Meeting && x.Name == searchUserCalender.meetingName && x.IsCancelled == false);

                    if (meeting != null) { meets.Add(meeting); }
                }
            }
            if (!string.IsNullOrEmpty(searchUserCalender.meetingOwner))
            {
                meets = await query.Where(m => m.CreatedBy == Guid.Parse(searchUserCalender.meetingOwner)).ToListAsync();

                var meetings = await Db.CalenderMeetings.Where(x => meetingIds.Contains(x.Id.ToString()) && x.ScheduleType == CalenderScheduleType.Meeting && x.CreatedBy == Guid.Parse(searchUserCalender.meetingOwner) && x.IsCancelled == false).ToListAsync();

                meets.AddRange(meetings);
            }

            foreach (var meet in meets)
            {
                var userMeetings = Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).ToList();
                int accepted = 0;
                int rejected = 0;
                int pending = 0;

                var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meet.MeetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
                meet.MeetingRecordingLink = recordingLink;

                Parallel.ForEach(userMeetings, x =>
                {
                    if (x.InviteResponse == InviteResponse.Yes) accepted++;
                    else if (x.InviteResponse == InviteResponse.No) rejected++;
                    else pending++;
                });
                CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
                {
                    Accepted = accepted,
                    Rejected = rejected,
                    Pending = pending
                };
                meet.MeetingResponse = meetResponse;
                meet.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId == meeting.Id).ToListAsync();
                var members = userMeetings.Select(x =>
                {
                    var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                    if (user != null)
                    {
                        return new UserMDVm
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email,
                            PhoneNumber = user.PhoneNumber,
                        };
                    }

                    return new UserMDVm();
                }).ToList();

                meet.Members = members.Where(m => m.Id != null || m.Email != null).ToList();
            }

            // Add response to redis cache
            await AddDataToRedis(searchUserCalender, cacheKey, meets);

            return meets;
        }
        #endregion

        #region Get Meeting By Id
        /// <summary>
        /// Get meeting by id
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        public async Task<CalenderMeeting> GetMeetingById(string meetingId)
        {
            //var cacheKey = $"Calender_{meetingId}";
            //Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            //var record = await _redisCacheService.GetDataAsync<CalenderMeeting>(cacheKey);
            //if (record != null)
            //{
            //    _logService.LogTypeResponse(meetingId, record, nameof(GetCalenderById), "Get Calender response from redis cache");
            //    return record;
            //}

            var calender = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
            .Where(x => x.Id.ToString() == meetingId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false)
            .FirstOrDefaultAsync();
            if (calender == null)
                return null;

            var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
            calender.MeetingRecordingLink = recordingLink;
            var attachments = await Db.CalenderUploads.Where(x => x.MeetingId == calender.Id).Select(x => x.FileName).ToListAsync();

            calender.AttachmentUrls = new List<string>();
            foreach (var file in attachments)
            {
                calender.AttachmentUrls.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
            };

            var members = await Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId).ToListAsync();
            int accepted = 0;
            int rejected = 0;
            int pending = 0;
            Parallel.ForEach(members, x =>
            {
                if (x.InviteResponse == InviteResponse.Yes) accepted++;
                else if (x.InviteResponse == InviteResponse.No) rejected++;
                else pending++;
            });
            CalendarMeetingResponse meetResponse = new CalendarMeetingResponse()
            {
                Accepted = accepted,
                Rejected = rejected,
                Pending = pending
            };
            calender.MeetingResponse = meetResponse;
            calender.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId.ToString() == meetingId).ToListAsync();

            var meetingOwner = Db.UserProfiles
                .Where(x => x.UserId == calender.CreatedBy.ToString())
                .Select(x => new UserMDVm
                {
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    Email = x.Email,
                    ProfilePictureUrl = x.ProfilePictureUrl,
                    Id = x.UserId
                }).FirstOrDefault();

            if (meetingOwner.ProfilePictureUrl is not null)
                meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);

            var meetingMembers = members.Select(x =>
            {
                var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                if (user != null)
                {
                    return new UserMDVm
                    {
                        Id = user.UserId,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                    };
                }

                return new UserMDVm();
            }).ToList();

            calender.ExternalMembers = members
                .Where(x => x.Email != null && x.UserId == null)
                .Select(x => x.Email).ToList();

            calender.Members = meetingMembers.Where(m => m.Id != null || m.Email != null).ToList();
            calender.MeetingOwner = meetingOwner;

            // Get proposed meeting dates
            calender.ProposedDateDetail = await GetProposedNewMeetingDateDeatils(meetingId);

            // Cache the response
            //var redisRes = await _redisCacheService.SetDataAsync<CalenderMeeting>(cacheKey, calender, DateTimeOffset.Now.AddDays(20));
            //if (!redisRes)
            //    _logService.LogTypeResponse(meetingId, calender, nameof(GetMeetingById), "Adding response to redis failed");

            return calender;

        }
        #endregion

        #region Get meeting by id - AI
        /// <summary>
        /// Get meeting using RTC meeting id - eg: JX8KL856HD or J6T-w39g-lhG
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="basicDetails"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetMeetingByRTCId(string meetingId, bool basicDetails = true)
        {
            ExternalMeeting externalMeeting = null;

            var meet = await Db.CalenderMeetings
                .Include(x => x.CustomFrequency)
                .Where(x => x.MeetingId == meetingId)
                .FirstOrDefaultAsync();

            if (meet is null)
            {
                // Check if the meeting is an external meeting
                externalMeeting = await Db.ExternalMeeting
                    .Where(x => x.MeetingId == meetingId)
                    .FirstOrDefaultAsync();
                if (externalMeeting is null)
                    return new GenericResponse
                    {
                        ResponseMessage = "Meeting does not exist",
                        ResponseCode = "400"
                    };
            }
            else
            {
                await ProcessMeetingParticipants(meet);
                meet.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId.ToString() == meet.Id.ToString()).ToListAsync();
            }

            if (basicDetails)
            {
                var response = new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                };

                if (externalMeeting is not null)
                {
                    response.Data = new
                    {
                        externalMeeting.Id,
                        externalMeeting.MeetingId,
                        Name = externalMeeting.MeetingName,
                        IsExternal = true,
                        IsPrivate = false,
                    };
                }
                else
                {
                    response.Data = new
                    {
                        meet.Id,
                        meet.MeetingId,
                        meet.Name,
                        IsExternal = false,
                        IsPrivate = meet.MakeSchdulePrivate,
                    };
                }

                return response;
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = new RTCMeetingResponse
                {
                    ExternalMeeting = externalMeeting,
                    InternalMeeting = meet
                }
            };
        }
        #endregion

        #region Add/Updates Meeting Notes
        /// <summary>
        /// Add or update meeting notes
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> AddOrUpdateMeetingNotes(AddMeetingNotesDto model)
        {
            var meeting = await Db.CalenderMeetings
                .FirstOrDefaultAsync(x => x.MeetingId == model.RtcMeetingId && x.IsCancelled == false);
            if (meeting == null)
                throw new RecordNotFoundException("Meeting not found");

            // Check if the user exists
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.UserId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            if (model.SubsequentMeetingId != null)
            {
                var subsequentMeetId = await Db.SubsequentMeetings
                    .FirstOrDefaultAsync(x => x.Id == model.SubsequentMeetingId && x.CalenderMeetingId == meeting.Id);
                if (subsequentMeetId == null)
                    throw new RecordNotFoundException("Subsequent meeting not found or does not belong to the main meeting id");
            }

            var meetingNotes = await Db.MeetingNotes
                            .FirstOrDefaultAsync(x => x.MeetingId == meeting.Id
                             && x.UserId == model.UserId
                             && (model.SubsequentMeetingId == null || x.SubsequentMeetingId == model.SubsequentMeetingId));

            if (meetingNotes == null)
            {
                meetingNotes = new MeetingNote
                {
                    MeetingId = meeting.Id,
                    SubsequentMeetingId = model.SubsequentMeetingId,
                    UserId = model.UserId,
                    Notes = model.Notes
                };
                await Db.MeetingNotes.AddAsync(meetingNotes);
            }
            else
            {
                meetingNotes.Notes = model.Notes;
                Db.MeetingNotes.Update(meetingNotes);
            }

            await Db.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Meeting notes added/updated successfully",
                Data = meetingNotes
            };
        }
        #endregion

        #region Get Meeting Notes
        /// <summary>
        /// Get meeting notes
        /// </summary>
        /// <param name="rtcMeetingId"></param>
        /// <param name="userId"></param>
        /// <param name="subSequentMeetingId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetMeetingNotes(string rtcMeetingId, string userId, Guid? subSequentMeetingId)
        {
            var meeting = await Db.CalenderMeetings
                .FirstOrDefaultAsync(x => x.MeetingId == rtcMeetingId && x.IsCancelled == false);
            if (meeting == null)
                throw new RecordNotFoundException("Meeting not found");

            var meetingNotes = await Db.MeetingNotes
                .Where(x => x.MeetingId == meeting.Id && x.UserId == userId && (subSequentMeetingId == null || x.SubsequentMeetingId == subSequentMeetingId))
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = meetingNotes
            };
        }
        #endregion

        #region Download Meeting Notes As PDF
        public async Task<GenericResponse> DownloadMeetingNotesAsPDF(string meetingNoteId)
        {
            var meetingNote = await Db.MeetingNotes
                .Include(x => x.Meeting)
                .FirstOrDefaultAsync(x => x.Id.ToString() == meetingNoteId);
            if (meetingNote == null)
                throw new RecordNotFoundException("Meeting notes not found");

            var parameters = new Dictionary<string, string>
                 {
                     { "{meeting_title}",  meetingNote.Meeting.Name},
                     { "{note_content}", meetingNote.Notes }
                 };

            var template = UpdateTemplateWithParams("note", _environment, parameters);
            //var pdfUrl = await _pdfService.GeneratePdfAsync(template);
            //var conversionRes = await HtmlToPdf.ConvertHtmlToPdfUssingP(template);
            var pdfBytes = await _pdfGeneratorServices.GenerateMeetingNotesPdfAsBytes();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = pdfBytes
            };
        }
        #endregion

        #region Send Meeting Notes As An Email
        public async Task<GenericResponse> SendMeetingNotesAsEmail(SendMeetingNotesDto model)
        {
            var meetingNote = await Db.MeetingNotes
                .Include(x => x.Meeting)
                .FirstOrDefaultAsync(x => x.Id.ToString() == model.MeetingId);
            if (meetingNote == null)
                throw new RecordNotFoundException("Meeting notes not found");

            foreach (var email in model.Emails)
            {
                var emailSent = await _emailService.SendEmail(model.MeetingNotes, email, $"Meeting Notes: {meetingNote.Meeting.Name}");
                if (!emailSent)
                    return new GenericResponse
                    {
                        ResponseCode = "500",
                        ResponseMessage = $"Failed to send email to {email}"
                    };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Email sent successfully"
            };
        }
        #endregion

        #region Delete Meeting Notes
        public async Task<GenericResponse> DeleteMeetingNotes(Guid meetingNoteId, CancellationToken cancellationToken = default)
        {
            var meetingNotes = await Db.MeetingNotes
                .FirstOrDefaultAsync(x => x.Id == meetingNoteId, cancellationToken);
            if (meetingNotes == null)
                throw new RecordNotFoundException("Meeting notes not found");

            Db.MeetingNotes.Remove(meetingNotes);
            await Db.SaveChangesAsync(cancellationToken);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Meeting notes deleted successfully"
            };
        }
        #endregion

        #region Get last held meeting for a reccuring meeting - AI
        /// <summary>
        /// Get the last meeting that held for a reccuring meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetAILastHeldMeetingForReccuringMeeting(string meetingId)
        {
            var lastHeldMeeting = await Db.SubsequentMeetings
                .Include(c => c.CalenderMeeting)
                .Where(x => x.SubsequentMeetingDateTime < DateTime.UtcNow && x.CalenderMeetingId.ToString() == meetingId)
                .OrderByDescending(x => x.SubsequentMeetingDateTime)
                .FirstOrDefaultAsync();

            await ProcessMeetingParticipants(lastHeldMeeting.CalenderMeeting);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = lastHeldMeeting
            };
        }
        #endregion

        #region To display the most recent meeting on the Dashboard By Id
        /// <summary>
        /// Get meeting by id
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        public async Task<CalenderMeeting> GetMostRecentMeetingById(string meetingId)
        {
            var cacheKey = $"Calender_{meetingId}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var record = await _redisCacheService.GetDataAsync<CalenderMeeting>(cacheKey);
            if (record != null)
            {
                _logService.LogTypeResponse(meetingId, record, nameof(GetCalenderById), "Get Calender response from redis cache");
                return record;
            }

            var calenders = await Db.CalenderMeetings.Include(x => x.CustomFrequency)
                                                     .Where(x => x.Id.ToString() == meetingId
                                                     && x.ScheduleType == CalenderScheduleType.Meeting
                                                     && x.IsCancelled == false)
                                                     .OrderByDescending(x => x.StartDate)
                                                     .ThenBy(x => x.EndTime)
                                                     .Take(4)
                                                     .ToListAsync();


            if (calenders == null)
                return null;

            var members = await Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId).ToListAsync();


            var memberList = members.Select(x => new UserMDVm
            {
                Id = x.UserId,
                Email = x.UserId == null ? x.Email : null,
            });

            CalenderMeeting calenderMeeting = new CalenderMeeting();

            foreach (var item in calenders)
            {
                if (item.HasCustomFrequency)

                    //item.Members = memberList.ToList();
                    //calenderMeeting.Members = item.Members;
                    calenderMeeting.Members = memberList.ToList();
                item.SubsequentMeetingDates = await Db.SubsequentMeetings.Where(s => s.CalenderMeetingId == item.Id).ToListAsync();
            }
            var recordingLink = await Db.CalenderMeetingRecordings.Where(x => x.MeetingId == meetingId).Select(x => x.MeetingRecordingUrl).FirstOrDefaultAsync();
            calenderMeeting.MeetingRecordingLink = recordingLink;

            // Cache the response
            var redisRes = await _redisCacheService.SetDataAsync(cacheKey, calenderMeeting, DateTimeOffset.Now.AddDays(20));
            if (!redisRes)
                _logService.LogTypeResponse(meetingId, calenderMeeting, nameof(GetMeetingById), "Adding response to redis failed");

            return calenderMeeting;

        }
        #endregion

        #region Remove Member From Calender Meeting or Event
        /// <summary>
        /// Remove member from calender meeting or event
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="calenderId"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> RemoveMemeberFromCalenderByUserId(string userId, string calenderId, string loggedInUserId, string externalMemberEmail = null)
        {
            var meeting = await Db.CalenderMeetings
                .FirstOrDefaultAsync(x => x.Id.ToString() == calenderId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false);
            if (meeting == null)
                throw new RecordNotFoundException("Meeting not found");

            // Check for permission
            if (loggedInUserId != userId)
                await CheckPermission(meeting.CreatedBy.ToString(), loggedInUserId);

            var result = 0;
            List<string> updatedExternalMembers = new List<string>();
            List<string> updatedInternalMembers = new List<string>();

            var internalMeetingMmembers = await Db.UserIdMeetingIds.Where(x => x.CalenderId == calenderId).AsNoTracking().ToListAsync();
            var externalMeetingMember = await Db.ExternalMeetingMembers.Where(x => x.ExternalMeetingId == calenderId).AsNoTracking().ToListAsync();

            if (userId is not null && internalMeetingMmembers.Any(x => x.UserId == userId))
            {
                var memberToRemove = internalMeetingMmembers.Where(x => x.UserId == userId).FirstOrDefault();
                Db.UserIdMeetingIds.Remove(memberToRemove);
                internalMeetingMmembers.Remove(memberToRemove);
                updatedInternalMembers = internalMeetingMmembers.Select(x => x.UserId).ToList();
            }
            if (externalMemberEmail is not null && internalMeetingMmembers.Any(x => x.Email == externalMemberEmail))
            {
                var memberToRemove = internalMeetingMmembers.Where(x => x.Email == externalMemberEmail).FirstOrDefault();
                Db.UserIdMeetingIds.Remove(memberToRemove);
                internalMeetingMmembers.Remove(memberToRemove);
                updatedInternalMembers = internalMeetingMmembers.Select(x => x.UserId).ToList();
            }
            if (userId is not null && externalMeetingMember.Any(x => x.UserId == userId))
            {
                var memberToRemove = externalMeetingMember.Where(x => x.UserId == userId).FirstOrDefault();
                Db.ExternalMeetingMembers.Remove(memberToRemove);
                externalMeetingMember.Remove(memberToRemove);
                updatedExternalMembers = externalMeetingMember.Select(x => x.UserId).ToList();
            }
            if (externalMemberEmail is not null && externalMeetingMember.Any(x => x.Email == externalMemberEmail))
            {
                var memberToRemove = externalMeetingMember.Where(x => x.Email == externalMemberEmail).FirstOrDefault();
                Db.ExternalMeetingMembers.Remove(memberToRemove);
                externalMeetingMember.Remove(memberToRemove);
                updatedExternalMembers = externalMeetingMember.Select(x => x.UserId).ToList();
            }
            //else
            //{
            //    throw new RecordNotFoundException("User was not invited to this meeting");

            //}

            // Update the meeting participants on rtc
            var payload = new UpdateParticipantRequest
            {
                CoHostJobAuthIds = new List<string>(),
                ExternalGuestEmails = updatedExternalMembers,
                GuestJobAuthIds = updatedInternalMembers
            };

            // Delete records from redis cache for assigned internal users
            var userMeetingIds = internalMeetingMmembers.Select(x => x.UserId).ToList();
            userMeetingIds.Add(meeting.CreatedBy.ToString());
            var redisResOne = await DeleteDataFromRedis(userMeetingIds);
            if (!redisResOne)
                _logService.LogTypeResponse(meeting.Id, meeting, nameof(CreateMeeting), "One or more redis cache keys could not be deleted");

            result = await Db.SaveChangesAsync();
            return result > 0 ? true : false;
        }
        #endregion

        #region Add Member From Calender Meeting or Event
        /// <summary>
        /// Remove member from calender meeting or event
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> AddMemberToCalender(AddMemberToMeeting request)
        {
            var meeting = await Db.CalenderMeetings
                .FirstOrDefaultAsync(x => x.Id.ToString() == request.CalenderId && x.ScheduleType == CalenderScheduleType.Meeting && x.IsCancelled == false);
            if (meeting == null)
                throw new RecordNotFoundException("Meeting not found");

            var inviteeDetails = await Db.UserProfiles.Where(x => x.UserId == meeting.CreatedBy.ToString()).FirstOrDefaultAsync();
            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
            var inviteeEmail = inviteeDetails.Email;

            // Check for permission
            // if (request.loggedInUserId != request.userId)
            await CheckPermission(meeting.CreatedBy.ToString(), request.LoggedInUserId);

            var inviteeEmails = new List<string>();
            var attendees = new Dictionary<string, string>();
            List<string> updatedExternalMembers = new List<string>();
            List<string> updatedInternalMembers = new List<string>();
            List<string> fileURLs = new List<string>();
            var uploads = await Db.CalenderUploads.Where(x => x.MeetingId == meeting.Id).Select(x => x.FileName).ToListAsync();
            if (uploads.Any())
            {
                foreach (var file in uploads)
                {
                    fileURLs.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
                };
            }

            var internalMeetingMmembers = await Db.UserIdMeetingIds.Where(x => x.CalenderId == request.CalenderId).AsNoTracking().ToListAsync();
            var externalMeetingMember = await Db.ExternalMeetingMembers.Where(x => x.ExternalMeetingId == request.CalenderId).AsNoTracking().ToListAsync();
            if (request.InternalMemberIds.Any() || request.ExternalTeamMembers.Any())
            {
                var invitedMmemberEmails = new List<string>();
                var invitedMmemberNames = new List<string>();
                var invitedUsers = new List<UserProfile>();
                var existingInternalMemberIds = internalMeetingMmembers.Select(x => x.UserId).ToList();
                var existingExternalMemberEmails = internalMeetingMmembers.Select(x => x.Email).ToList();
                request.InternalMemberIds = request.InternalMemberIds.Where(x => !existingInternalMemberIds.Contains(x)).ToList();
                var invites = request.InternalMemberIds.Select(x => new UserIdCalenderId()
                {
                    UserId = x,
                    CalenderId = meeting.Id.ToString()
                });
                Db.UserIdMeetingIds.AddRange(invites);

                foreach (var invitedUser in request.InternalMemberIds)
                {
                    if (existingInternalMemberIds.Contains(invitedUser))
                        continue;
                    var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                    if (user is null)
                        throw new RecordNotFoundException("User not found");

                    invitedUsers.Add(user);
                    invitedMmemberEmails.Add(user.Email);
                    invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                    attendees.Add(user.Email, user.UserId);

                    // Send notification to the user
                    var notification = new AddNotificationDto
                    {
                        Message = $"You have been invited to a meeting - {meeting.Name} by {invitee}",
                        Event = EventCategory.Calender,
                        EventId = meeting.Id.ToString(),
                        CreatedBy = meeting.CreatedBy.ToString(),
                    };
                    var notificationId = await AddNotification(notification);
                    if (notificationId is not null)
                    {
                        await AddUserNotification(new List<string> { invitedUser }, Guid.Parse(notificationId));
                    }
                }
                await Db.SaveChangesAsync();

                // Calculate and update the subsequent meeting dates table
                var (aiName, aiImage) = await GetAiDetails(request.Token);

                // Send mail to external team member
                request.ExternalTeamMembers = request.ExternalTeamMembers.Where(x => !existingExternalMemberEmails.Contains(x)).ToList();
                if (request.ExternalTeamMembers.Any() && request?.ExternalTeamMembers[0] != null)
                {
                    request.ExternalTeamMembers.ForEach(x =>
                    {

                        if (!attendees.ContainsKey(x)) attendees.Add(x, null);
                    });
                    inviteeEmails.AddRange(request.ExternalTeamMembers);
                    var externalTeamMembers = request.ExternalTeamMembers.Select(x => new UserIdCalenderId()
                    {
                        CalenderId = meeting.Id.ToString(),
                        Email = x
                    });

                    Db.UserIdMeetingIds.AddRange(externalTeamMembers);                 
                }

                var env = new CalenderEventDto
                {
                    Title = meeting.Name,
                    Description = meeting.Name,
                    Location = meeting.Location,
                    StartDateAndTime = meeting.StartDate,
                    EndDateAndTime = meeting.EndTime.Value,
                    Attendees = attendees,
                    Organizer = inviteeDetails,
                    MeetingId = meeting.Id.ToString(),
                    MeetingLink = meeting.MeetingLink,
                    subdomain = request.Subdomain,
                    Frequency = meeting?.Frequency,
                    CustomFrequencyDto = null,
                };

                // Add the organizer to the attendees
                if (!env.Attendees.ContainsKey(env.Organizer.Email))
                    env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);

                var icalenderStream = CalenderHelper.CreateCalenderEvent(env);

                var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
                var templateForSubsequentMeetings = string.Empty;
                invitedMmemberNames.AddRange(request?.ExternalTeamMembers);
                var message = string.Format("{0} has invited you to a meeting.", invitee);
                var title = string.Format(" Meeting Invitation - {0}", meeting.Name);
                if (invitedMmemberEmails.Any())
                {
                    inviteeEmails.AddRange(invitedMmemberEmails);
                    SendMailToInternalMembers(request.Subdomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, invitedUsers, false, title, message, aiName, aiImage, fileURLs, false, icalenderStream);
                }

                if (request.ExternalTeamMembers.Any() && request?.ExternalTeamMembers[0] != null)
                {
                    await Db.SaveChangesAsync();
                    SendMailToExternalMembers(request.Subdomain, invitee, inviteeEmail, meeting, templateFromFolder, invitedMmemberNames, false, request.ExternalTeamMembers, "Meeting Invitation", message, aiName, aiImage, invitee, fileURLs, false, icalenderStream);
                }
            }

            await Db.SaveChangesAsync();

            // Update the meeting participants on rtc
            var payload = new UpdateParticipantRequest
            {
                CoHostJobAuthIds = new List<string>(),
                ExternalGuestEmails = updatedExternalMembers,
                GuestJobAuthIds = updatedInternalMembers
            };

            // Delete records from redis cache for assigned internal users
            var userMeetingIds = internalMeetingMmembers.Select(x => x.UserId).ToList();
            userMeetingIds.Add(meeting.CreatedBy.ToString());
            var redisResOne = await DeleteDataFromRedis(userMeetingIds);
            if (!redisResOne)
                _logService.LogTypeResponse(meeting.Id, meeting, nameof(CreateMeeting), "One or more redis cache keys could not be deleted");

            _logService.LogTypeResponse(request, meeting, nameof(CreateMeeting), "Add user to meeting");


            return true;
        }
        #endregion

        #region Update or Reschedule Subsequent Meeting
        /// <summary>
        /// Update meeting or event
        /// </summary>
        /// <param name="updateCalenderDto"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<SubsequentMeeting> UpdateSubsequentMeeting(UpdateSubsequentMeetingDto updateCalenderDto)
        {
            if (updateCalenderDto.AIResheduleForMeetingsThatDidntHappen)
            {
                Db = new JobProDbContext(_conString, new DbContextSchema(updateCalenderDto.SubDomain));
            }

            // Get the invitee name
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == updateCalenderDto.UserId)
                .FirstOrDefault();
            if (inviteeDetails == null)
                throw new RecordNotFoundException("User not found");

            if (!updateCalenderDto.AIResheduleForMeetingsThatDidntHappen)
            {
                var hasPermission = await CheckUserPermission(updateCalenderDto.UserId, Permissions.can_create_meetings);
                if (!hasPermission)
                    throw new UnauthorizedAccessException("You do not have the permission to update or reshedule this meeting");
            }

            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
            var inviteeEmail = inviteeDetails.Email;

            // Check if a meeting or event exist with the supplied calender id
            var subsequentMeeting = Db.SubsequentMeetings
                .Include(x => x.CalenderMeeting)
                .Where(x => x.Id == updateCalenderDto.SubsequentMeetingId).FirstOrDefault();
            if (subsequentMeeting == null) { throw new RecordNotFoundException("Meeting or event does not exist"); }

            if (subsequentMeeting.IsCanceled)
                throw new InvalidOperationException("Update is not allowed. Meeting has been cancelled");

            var calender = Db.CalenderMeetings
                .Where(x => x.Id == subsequentMeeting.CalenderMeetingId).FirstOrDefault();

            // Check for permission
            await CheckPermission(subsequentMeeting.CalenderMeeting.CreatedBy.ToString(), updateCalenderDto.UserId);

            // Update the meeting or event
            int duration = Convert.ToInt32(updateCalenderDto.EndTime.TimeOfDay.TotalMinutes - updateCalenderDto.StartDate.TimeOfDay.TotalMinutes);
            if (duration / 60 == 0)
                updateCalenderDto.MeetingDuration = duration.ToString() + " mins";
            else
                updateCalenderDto.MeetingDuration = (duration / 60).ToString() + "hrs " + (duration % 60).ToString() + "mins";

            subsequentMeeting.Name = updateCalenderDto.Name;
            subsequentMeeting.SubsequentMeetingDateTime = updateCalenderDto.StartDate;
            subsequentMeeting.EndTime = updateCalenderDto.EndTime;
            subsequentMeeting.MeetingDuration = updateCalenderDto.MeetingDuration;
            subsequentMeeting.NotifyMe = updateCalenderDto.NotifyMe;
            subsequentMeeting.MakeSchdulePrivate = updateCalenderDto.MakeSchdulePrivate;
            subsequentMeeting.NotifyMembersIn = updateCalenderDto.NotifyMeInMinutes;
            subsequentMeeting.RescheduleCount = updateCalenderDto.RescheduleCount;
            Db.SubsequentMeetings.Update(subsequentMeeting);

            //Convert Base64 to iFormFile and upload
            var fileURLs = new List<string>();
            if (updateCalenderDto?.AttachmentBase64 is not null && (bool)updateCalenderDto?.AttachmentBase64.Any())
            {
                var attachments = new List<IFormFile>();
                foreach (var attachment in updateCalenderDto.AttachmentBase64)
                {
                    var file = Utility.ConvertBase64ToFile(attachment);
                    attachments.Add(file);
                };

                //Upload the IFormFile using initial Upload Document Service
                var uploads = await UploadDocument(new UploadDocumentDto()
                {
                    SubsequentMeetingId = subsequentMeeting.Id.ToString(),
                    Files = attachments
                });

                //Get Signed URL for each uploaded file to be used in email notification
                foreach (var file in uploads)
                {
                    fileURLs.Add(_aWSS3Sevices.GetSignedUrlAsync(file).Result);
                };

            }

            // Delete meeting members
            var userMeetingIds = Db.UserIdMeetingIds.Where(x => x.SubsequentMeetingId == subsequentMeeting.Id.ToString()).ToList();
            if (userMeetingIds.Count > 0)
                Db.UserIdMeetingIds.RemoveRange(userMeetingIds);

            // Add the new user meeting ids
            var userMeetingIdsToAdd = new List<UserIdCalenderId>();
            foreach (var item in updateCalenderDto.InvitedUsers)
            {
                userMeetingIdsToAdd.Add(new UserIdCalenderId
                {
                    SubsequentMeetingId = subsequentMeeting.Id.ToString(),
                    UserId = item
                });
            }

            // Add new external team member to database
            var externalTeamMembers = updateCalenderDto.ExternalTeamMemberEmails.Select(x => new UserIdCalenderId()
            {
                SubsequentMeetingId = subsequentMeeting.Id.ToString(),
                Email = x
            });

            await Db.UserIdMeetingIds.AddRangeAsync(userMeetingIdsToAdd);
            await Db.UserIdMeetingIds.AddRangeAsync(externalTeamMembers);
            var result = await Db.SaveChangesAsync();

            var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
            var templateForSubsequentMeetings = string.Empty;

            // var templateFromFolder = File.ReadAllText(templatePath);

            var title = $"Meeting Invitation Update: {updateCalenderDto.Name}";
            var inviteeEmails = new List<string>() { inviteeDetails.Email };

            if (result > 0)
            {
                // Calculate and update the subsequent meeting dates table
                if (updateCalenderDto.ReoccuringDeleteOptions != ReoccuringDeleteOptions.ThisOnly)
                {
                    calender.StartDate = updateCalenderDto.StartDate;
                    calender.Name = updateCalenderDto.Name;
                    calender.NotifyMe = updateCalenderDto.NotifyMe;
                    calender.NotifyMembersIn = updateCalenderDto.NotifyMeInMinutes;
                    calender.EndTime = updateCalenderDto.EndTime;
                    calender.EndDate = calender.EndTime;
                    calender.MeetLength = duration;
                    calender.MeetingDuration = updateCalenderDto.MeetingDuration;
                    calender.MakeSchdulePrivate = updateCalenderDto.MakeSchdulePrivate;

                    await _backGroundService.AddMeetingSubsequentDates(new CalenderVm
                    {
                        Frequency = updateCalenderDto.Frequency,
                        CustomFrequency = updateCalenderDto.CustomFrequency,
                        SubDomain = updateCalenderDto.SubDomain,
                        ReoccuringDeleteOptions = updateCalenderDto.ReoccuringDeleteOptions,
                    }, calender);
                }

                var invitedMmemberEmails = new List<string>();
                var invitedMmemberNames = new List<string>();
                var invitedUsers = new List<UserProfile>();
                var attendees = new Dictionary<string, string>();
                var message = string.Format("{0} has invited you to a meeting.", invitee);
                if (updateCalenderDto.InvitedUsers.Any())
                {
                    foreach (var invitedUser in updateCalenderDto.InvitedUsers)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                        if (user is null) continue;
                        invitedUsers.Add(user);
                        invitedMmemberEmails.Add(user?.Email);
                        invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                        if (!attendees.ContainsKey(user.Email))
                            attendees.Add(user.Email, user.UserId);

                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been invited to a meeting - {subsequentMeeting.Name} by {invitee}",
                            Event = EventCategory.Calender,
                            EventId = subsequentMeeting.Id.ToString(),
                            CreatedBy = updateCalenderDto.UserId,
                        };
                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                        {
                            await AddUserNotification(new List<string> { invitedUser }, Guid.Parse(notificationId));
                        }

                        await Db.SaveChangesAsync();
                    }
                    inviteeEmails.AddRange(invitedMmemberEmails);
                }

                // Send an iCalender file to the invitees and the meeting owner
                var env = new CalenderEventDto
                {
                    Title = calender.Name,
                    Description = calender.Name,
                    Location = calender.Location,
                    StartDateAndTime = calender.StartDate,
                    EndDateAndTime = calender.EndTime.Value,
                    Attendees = attendees,
                    Organizer = inviteeDetails,
                    MeetingId = calender.Id.ToString(),
                    MeetingLink = calender.MeetingLink,
                    subdomain = updateCalenderDto.SubDomain,
                    Frequency = updateCalenderDto.Frequency,
                    CustomFrequencyDto = updateCalenderDto.Frequency != null ? null : updateCalenderDto.CustomFrequency,
                };

                // Add the organizer to the attendees
                env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);
                var icalenderStream = CalenderHelper.UpdateCalenderEvent(env);

                var (aiName, aiImage) = await GetAiDetails(updateCalenderDto.Token);
                if (subsequentMeeting.NotifyMe == NotifyMeVia.Email || subsequentMeeting.NotifyMe == NotifyMeVia.Both)
                {
                    SendMailToInternalMembers(updateCalenderDto.SubDomain, invitee, inviteeEmail, calender, templateFromFolder, invitedMmemberNames, invitedUsers, true, title, message, aiName, aiImage, fileURLs, false, icalenderStream);
                }

                // Send mail to external team member
                if (updateCalenderDto.ExternalTeamMemberEmails.Any())
                {
                    updateCalenderDto.ExternalTeamMemberEmails.ForEach(x =>
                    {
                        if (!attendees.ContainsKey(x))
                            attendees.Add(x, null);
                    });

                    inviteeEmails.AddRange(updateCalenderDto.ExternalTeamMemberEmails);
                    invitedMmemberNames.AddRange(updateCalenderDto.ExternalTeamMemberEmails);
                    SendMailToExternalMembers(updateCalenderDto.SubDomain, invitee, inviteeEmail, calender, templateFromFolder, invitedMmemberNames, true, updateCalenderDto.ExternalTeamMemberEmails, title, message, aiName, aiImage, invitee, fileURLs, true, icalenderStream);
                }

                // Delete records from redis cache for assigned internal users
                updateCalenderDto.InvitedUsers.Add(updateCalenderDto.UserId);
                await DeleteDataFromRedis(updateCalenderDto.InvitedUsers);

                return subsequentMeeting;
            }

            return null;
        }
        #endregion

        #region Cancel Meeting - Internal
        /// <summary>
        /// Cancel a meeting or event
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="subdomain"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> CancelMeeting(string meetingId, CancelMeetingVm model, string subdomain)
        {
            // Get the invitee name
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == model.UserId)
                .FirstOrDefault();
            if (inviteeDetails == null) { throw new RecordNotFoundException("user does not exist"); }
            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;

            // Check if a meeting or event exist with the supplied calender id
            var calender = Db.CalenderMeetings.Where(x => x.Id.ToString() == meetingId).FirstOrDefault();
            if (calender == null) { throw new RecordNotFoundException("Meeting or event does not exist"); }

            // Check for permission
            await CheckPermission(calender.CreatedBy.ToString(), model.UserId);

            if (calender.IsCancelled) { throw new InvalidOperationException("Meeting has already been cancelled"); }

            calender.IsCancelled = true;
            Db.CalenderMeetings.Update(calender);
            var dbResult = await Db.SaveChangesAsync();

            if (dbResult > 0)
            {
                // Get meeting members
                var userMeetingIds = Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId).Select(x => x.UserId).ToList();

                // Check if the meeting has a schedule background job for 'SendNotificationsForReoccurringMeetings'
                if (calender.Frequency == MeetingFrequency.OneOff.ToString())
                {
                    if (_cache.TryGetValue(meetingId, out string jobId))
                    {
                        BackgroundJob.Delete(jobId);
                        _cache.Remove(meetingId);
                    }
                }
                else
                {
                    var jobId = await GetJobId(meetingId);
                    if (!string.IsNullOrEmpty(jobId))
                    {
                        BackgroundJob.Delete(jobId);
                        await RemoveJobId(meetingId);
                    }
                }

                var templateFromFolder = ReadTemplateFromFile("meeting_template_new", _environment);
                templateFromFolder = templateFromFolder.Replace("{visibility}", "display: none;");
                var invitedMmemberProfile = new List<UserProfile>();
                var templateForSubsequentMeetings = string.Empty;
                var message = !string.IsNullOrEmpty(model.Message) ? model.Message : string.Format("{0} has cancelled the meeting.", invitee);
                var invitedMmemberNames = new List<string>();
                var (aiName, aiImage) = await GetAiDetails(model.Token);

                // Send mail to invited members that the meeting has been cancelled
                if (calender.NotifyMe == NotifyMeVia.Email || calender.NotifyMe == NotifyMeVia.Both)
                {
                    // Get the invited members
                    var invitedMmemberIds = Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId)
                        .Select(x => x.UserId).ToList();

                    var invitedMmemberEmails = new List<string>();
                    foreach (var invitedUser in invitedMmemberIds)
                    {
                        if (string.IsNullOrEmpty(invitedUser)) continue;

                        var userProfile = await Db.UserProfiles.Where(u => u.UserId == invitedUser).FirstOrDefaultAsync();
                        invitedMmemberEmails.Add(userProfile.Email);
                        invitedMmemberProfile.Add(userProfile);
                        invitedMmemberNames.Add(userProfile.FirstName + " " + userProfile.LastName);
                    }

                    if (invitedMmemberProfile.Any())
                        SendMailToInternalMembers(subdomain, invitee, inviteeDetails.Email, calender, templateFromFolder, invitedMmemberNames, invitedMmemberProfile, false, $"Cancelled Event: {calender.Name}", message, aiName, aiImage, null);
                }

                // Get the external team members and Send mail to external team member
                var invitedExternalMmemberEmails = Db.UserIdMeetingIds.Where(x => x.CalenderId == meetingId)
                        .Select(x => x.Email).ToList();
                if (invitedExternalMmemberEmails.Any() && !string.IsNullOrEmpty(invitedExternalMmemberEmails[0]))
                {
                    SendMailToExternalMembers(subdomain, invitee, inviteeDetails.Email, calender, templateFromFolder, invitedMmemberNames, false, invitedExternalMmemberEmails, $"Cancelled Event: {calender.Name}", message, aiName, aiImage, null, null);
                }

                // Delete records from redis cache for assigned internal users
                userMeetingIds.Add(calender.CreatedBy.ToString());
                var redisResOne = await DeleteDataFromRedis(userMeetingIds);
                if (!redisResOne)
                    _logService.LogTypeResponse(meetingId, calender, nameof(CreateMeeting), "One or more redis cache keys could not be deleted");

                return true;
            }

            return false;
        }
        #endregion

        #region Add Personal Schedule
        /// <summary>
        /// Adds personal schesules
        /// </summary>
        /// <param name="personalSchesules"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<List<PersonalSchedule>> AddPersonalSchedule(List<PersonalScheduleDto> personalSchesules)
        {
            var personalSch = _mapper.Map<List<PersonalSchedule>>(personalSchesules);
            //personalSch.ForEach(x => x.Day = x.Date.DayOfWeek.ToString());

            var personalSchToAdd = new List<PersonalSchedule>();
            foreach (var item in personalSch)
            {
                // Validate that ExternalMeetingId is not null when ScheduleName is Custom
                if (item.ScheduleName == PersonalScheduleType.Custom && string.IsNullOrEmpty(item.MeetingId))
                {
                    throw new DirtyFormException($"MeetingId is required when ScheduleName is Custom");
                }

                if (item.StartTime >= item.EndTime)
                    throw new DirtyFormException($"Start time - {item.StartTime} for {item.Day} cannot be greater than or equal to End time - {item.EndTime}");

                var personalSchExist = Db.PersonalSchedule.Where(x => x.UserId == item.UserId
                && x.Day == item.Day).ToList();
                if (personalSchExist.Count > 0)
                {
                    foreach (var sch in personalSchExist)
                    {
                        // Check if the time has already been selected
                        var alreadySelected = false;

                        if (sch.StartTime < item.EndTime && sch.EndTime > item.StartTime)
                            alreadySelected = true;

                        if (alreadySelected && item.ScheduleName != PersonalScheduleType.Custom)
                            throw new ArgumentException($"The time range has already been selected for {sch.Day}");

                        personalSchToAdd.Add(item);
                    }
                }
                else
                    personalSchToAdd.Add(item);

            }

            await Db.PersonalSchedule.AddRangeAsync(personalSchToAdd);
            var result = await Db.SaveChangesAsync();

            // Delete records from redis cache
            var listOfUserIds = personalSchesules.Select(x => x.UserId.ToString()).ToList();
            await DeleteDataFromRedis(listOfUserIds);

            return result > 0 ? personalSchToAdd : null;
        }
        #endregion

        #region Update Personal Schedule
        /// <summary>
        /// Update Personal Schedule
        /// </summary>
        /// <param name="personalScheduleDtos"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdatePersonalSchedule(List<PersonalScheduleDto> personalScheduleDtos)
        {
            var personalSchsToUpdate = new List<PersonalSchedule>();
            var personalSchToUpdateDto = new List<PersonalScheduleDto>();
            personalScheduleDtos = personalScheduleDtos.OrderBy(x => ((int)(DayOfWeek)Enum.Parse(typeof(DayOfWeek), x.Day, true) + 1) % 7 + 1).ThenBy(p => p.StartTime).ToList();

            // Comfirm that the time ranges are not overlapping with each other
            var timeRanges = new Dictionary<string, List<(DateTime startTime, DateTime endTime)>>();
            foreach (var sch in personalScheduleDtos)
            {
                if (sch.StartTime >= sch.EndTime)
                    throw new DirtyFormException($"Start time - {sch.StartTime} for {sch.Day} cannot be greater than or equal to End time - {sch.EndTime}");

                if (timeRanges.ContainsKey(sch.Day))
                    timeRanges[sch.Day].Add((sch.StartTime, sch.EndTime));
                else
                    timeRanges.Add(sch.Day, new List<(DateTime startTime, DateTime endTime)> { (sch.StartTime, sch.EndTime) });
            }

            var counter = 0;
            DateTime? tempST = null;
            DateTime? tempET = null;
            foreach (var sch in personalScheduleDtos)
            {
                foreach (var range in timeRanges[sch.Day])
                {
                    if (counter == 0)
                    {
                        personalSchToUpdateDto.Add(sch);
                        counter++;
                    }
                    else
                    {
                        if (tempET is null || tempST is null)
                        {

                            if (sch.StartTime >= range.endTime)
                            {
                                personalSchToUpdateDto.Add(sch);
                            }
                            else
                                throw new ArgumentException($"The time range: {sch.StartTime.ToShortTimeString()} - {sch.EndTime.ToShortTimeString()} overlaps with another selected time range");

                            tempST = sch.StartTime;
                            tempET = sch.EndTime;
                        }
                        else
                        {
                            if (sch.StartTime >= tempET)
                            {
                                personalSchToUpdateDto.Add(sch);
                            }
                            else
                                throw new ArgumentException($"The time range: {sch.StartTime.ToShortTimeString()} - {sch.EndTime.ToShortTimeString()} overlaps with another selected time range");

                            tempST = sch.StartTime;
                            tempET = sch.EndTime;
                        }
                    }
                }

                counter = 0;
            }

            if (personalSchToUpdateDto.Count == 0) { return false; }

            // Get the personal schedule to be updated
            foreach (var item in personalSchToUpdateDto.Distinct())
            {
                var personalSchToUpdate = await Db.PersonalSchedule.Where(x => x.UserId == item.UserId && x.Id.ToString() == item.Id).FirstOrDefaultAsync();
                if (personalSchToUpdate is null)
                    continue;

                // Validate that ExternalMeetingId is not null when ScheduleName is Custom
                if (item.ScheduleName == PersonalScheduleType.Custom && string.IsNullOrEmpty(item.MeetingId))
                {
                    throw new DirtyFormException($"ExternalMeetingId is required when ScheduleName is Custom");
                }

                // Update the personal schedule
                personalSchToUpdate.StartTime = item.StartTime;
                personalSchToUpdate.EndTime = item.EndTime;
                personalSchToUpdate.AvailableForSprintsEmergencyHours = item.AvailableForSprintsEmergencyHours;
                personalSchToUpdate.AvailableForMeetingsEmergencyHours = item.AvailableForMeetingsEmergencyHours;
                personalSchToUpdate.AvailableForTasksEmergencyHours = item.AvailableForTasksEmergencyHours;
                personalSchToUpdate.ScheduleIsPublic = item.ScheduleIsPublic;
                personalSchToUpdate.TeamMatesCanSee = item.TeamMatesCanSee;
                personalSchToUpdate.ScheduleName = item.ScheduleName;
                personalSchToUpdate.Available = item.Available;
                personalSchToUpdate.MeetingId = item.MeetingId;

                personalSchsToUpdate.Add(personalSchToUpdate);
            }

            if (!personalSchsToUpdate.Any())
                throw new RecordNotFoundException("No schedule record found for the supplied userId");

            Db.PersonalSchedule.UpdateRange(personalSchsToUpdate);
            var result = await Db.SaveChangesAsync();

            // Delete records from redis cache
            var listOfUserIds = personalScheduleDtos.Select(x => x.UserId.ToString()).ToList();
            await DeleteDataFromRedis(listOfUserIds);

            return result > 0 ? true : false;
        }
        #endregion

        #region Get Personal Schedule By UserId
        /// <summary>
        /// Get personal schedule by userId
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<PersonalSchedule>> GetPersonalScheduleByUserId(string userId)
        {
            var cacheKey = $"{_redisKey}_schedule_{userId}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);
            var records = await _redisCacheService.GetDataAsync<List<PersonalSchedule>>(cacheKey);
            if (records != null)
            {
                _logService.LogTypeResponse(userId, records, nameof(GetPersonalScheduleByUserId), "Get Personal Schedule response from redis cache");
                return records;
            }

            var personalSchedules = await Db.PersonalSchedule.Where(x => x.UserId.ToString() == userId).ToListAsync();

            // Add records to cache
            await AddDataToRedis(userId, cacheKey, personalSchedules);
            return personalSchedules;
        }
        #endregion

        #region Get Personal Schedules For A List Of Userids
        /// <summary>
        /// Get Personal Schedules For A List Of Userids
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public async Task<List<PersonalSchedule>> GetPersonalSchedules(List<string> userIds)
        {
            return await Db.PersonalSchedule
                .Where(x => userIds.Contains(x.UserId.ToString())).ToListAsync();
        }
        #endregion

        #region Delete Personal Schedule
        /// <summary>
        /// Delete a user's personal schedule or all expired custom schedules
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="UnauthorizedAccessException"></exception>
        public async Task<GenericResponse> DeletePersonalSchedule(DeletePersonalScheduleDto model)
        {
            // If DeleteExpiredSchedules is true, delete all expired custom schedules
            if (model.DeleteExpiredSchedules)
            {
                // Get all custom personal schedules for this user
                var customSchedules = await Db.PersonalSchedule
                    .Where(x => x.UserId.ToString() == model.UserId &&
                           x.ScheduleName == PersonalScheduleType.Custom &&
                           x.MeetingId != null)
                    .ToListAsync();

                if (!customSchedules.Any())
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "No expired custom schedules found",
                        Data = null
                    };

                // Get the external meeting IDs from the custom schedules
                var externalMeetingIds = customSchedules
                    .Where(x => x.MeetingId != null)
                    .Select(x => x.MeetingId)
                    .Distinct()
                    .ToList();

                // Get the external meetings with expired CanBookEndDate
                var expiredExternalMeetings = await Db.ExternalMeeting
                    .Where(x => externalMeetingIds.Contains(x.MeetingId) &&
                           x.CanBookEndDate.HasValue &&
                           x.CanBookEndDate.Value < DateTime.UtcNow)
                    .ToListAsync();

                if (!expiredExternalMeetings.Any())
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "No expired custom schedules found",
                        Data = null
                    };

                // Get the IDs of expired external meetings
                var expiredExternalMeetingIds = expiredExternalMeetings.Select(x => x.MeetingId).ToList();

                // Filter custom schedules to only include those associated with expired external meetings
                var schedulesToDelete = customSchedules
                    .Where(x => x.MeetingId != null && expiredExternalMeetingIds.Contains(x.MeetingId))
                    .ToList();

                if (!schedulesToDelete.Any())
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "No expired custom schedules found",
                        Data = null
                    };

                // Check if any of the schedules are primary schedules for external meetings
                foreach (var schedule in schedulesToDelete)
                {
                    var externalMeeting = expiredExternalMeetings.FirstOrDefault(x => x.PersonalScheduleId == schedule.Id);
                    if (externalMeeting != null)
                    {
                        // Skip this schedule as it's a primary schedule
                        continue;
                    }

                    // Delete the schedule
                    Db.PersonalSchedule.Remove(schedule);
                }

                var result = await Db.SaveChangesAsync();

                // Clear cache for this user
                await DeleteDataFromRedis(new List<string> { model.UserId });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Successfully deleted {result} expired custom schedules",
                    Data = schedulesToDelete.Count
                };
            }
            else
            {
                // Regular single schedule deletion
                if (string.IsNullOrEmpty(model.PersonalScheduleId))
                    throw new DirtyFormException("Personal schedule ID is required when not deleting expired schedules");

                // Find the personal schedule
                var personalSchedule = await Db.PersonalSchedule
                    .FirstOrDefaultAsync(x => x.Id.ToString() == model.PersonalScheduleId);

                if (personalSchedule == null)
                    throw new RecordNotFoundException("Personal schedule not found");

                // Check if the user is authorized to delete this schedule
                if (personalSchedule.UserId.ToString() != model.UserId)
                    throw new UnauthorizedAccessException("You are not authorized to delete this personal schedule");

                // Check if this schedule is associated with an external meeting
                if (personalSchedule.MeetingId != null)
                {
                    // Check if this is the only personal schedule for this external meeting
                    var countOfSchedulesForMeeting = await Db.PersonalSchedule
                        .CountAsync(x => x.MeetingId == personalSchedule.MeetingId);

                    if (countOfSchedulesForMeeting == 1)
                    {
                        // This is the only schedule, check if it's referenced by an external meeting
                        var externalMeeting = await Db.ExternalMeeting
                            .FirstOrDefaultAsync(x => x.PersonalScheduleId == personalSchedule.Id);

                        if (externalMeeting != null)
                            throw new InvalidOperationException("Cannot delete this schedule as it is the primary schedule for an external meeting. Please update the external meeting first.");
                    }
                }

                // Delete the personal schedule
                Db.PersonalSchedule.Remove(personalSchedule);
                var result = await Db.SaveChangesAsync();

                // Clear cache for this user
                await DeleteDataFromRedis(new List<string> { model.UserId });

                return new GenericResponse
                {
                    ResponseCode = result > 0 ? "200" : "500",
                    ResponseMessage = result > 0 ? "Personal schedule deleted successfully" : "Failed to delete personal schedule",
                    Data = personalSchedule
                };
            }
        }
        #endregion

        #region Create External Meeting
        /// <summary>
        /// Create External One On One Meeting
        /// </summary>
        /// <param name="externalMeetingDto"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>ExternalMeetingDto
        public async Task<ExternalMeeting> CreatExternalMeetingOrEvent(ExternalMeetingDto externalMeetingDto)
        {
            // Map External Meeting
            var externalMeeting = externalMeetingDto.Map();
            var meetingId = GenerateMeetingId();

            List<PersonalSchedule> personalSchs = new List<PersonalSchedule>();
            var result = 0;

            if (externalMeetingDto.PersonalSchedule == PersonalScheduleType.Custom && !externalMeetingDto.PersonalScheduleDtos.Any())
                throw new DirtyFormException($"At least one Personal Schedule is required when ScheduleName is Custom");

            if (externalMeetingDto.PersonalScheduleDtos.Any())
            {
                if (externalMeetingDto.PersonalSchedule != PersonalScheduleType.Custom)
                    throw new DirtyFormException($"Custom Personal Schedule Type is required when ScheduleName is Custom");

                externalMeetingDto.PersonalScheduleDtos.ForEach(x => x.MeetingId = meetingId);
                personalSchs = await AddPersonalSchedule(externalMeetingDto.PersonalScheduleDtos);
            }
            else
                personalSchs = await Db.PersonalSchedule
                    .Where(x => x.ScheduleName == externalMeetingDto.PersonalSchedule && x.UserId.ToString() == externalMeetingDto.UserId).ToListAsync();

            // Payload validations
            await PayloadValidationsForExternalMeetings(externalMeetingDto, personalSchs);

            // Get the invitee name
            var inviteeDetails = await Db.UserProfiles.Where(x => x.UserId == externalMeetingDto.UserId)
                .FirstOrDefaultAsync();
            if (inviteeDetails == null)
                throw new RecordNotFoundException("User not found");

            var invitee = inviteeDetails.FirstName + " " + inviteeDetails.LastName;
            var inviteeEmail = inviteeDetails.Email;

            // Map External Meeting Questions
            var externalMeetingQuestions = _mapper.Map<ExternalMeetingQuestion>(externalMeetingDto.ExternalMeetingQuestionSetting);
            //this.Db.ExternalMeetingQuestion.Add(externalMeetingQuestions);

            var tenantId = await Dbo.Tenants.Where(x => x.Subdomain.ToLower() == externalMeetingDto.SubDomain.ToLower())
                .Select(x => x.Id).FirstOrDefaultAsync();

            externalMeeting.ExternalMeetingQuestionId = externalMeetingQuestions.Id;
            externalMeeting.PersonalScheduleId = personalSchs[0].Id;
            externalMeeting.MeetingId = meetingId;
            externalMeeting.ExternalMeetingQuestion = externalMeetingQuestions;
            externalMeeting.CreatedBy = Guid.Parse(externalMeetingDto.UserId);
            externalMeeting.BookingLink = string.Format(Utility.Constants.BOOKING_LINK, externalMeetingDto.SubDomain, externalMeeting.Id);
            externalMeeting.MeetingLink = GenerateMeetingLink(externalMeetingDto.SubDomain, meetingId);

            var otherMeetingHosts = new List<UserProfile>() { inviteeDetails };
            otherMeetingHosts = await Db.UserProfiles.Where(x => externalMeetingDto.OtherMeetingHostsIds.Contains(x.UserId))
                            .ToListAsync();
            _cache.Set(externalMeeting.MeetingId, otherMeetingHosts);

            if (externalMeetingDto.InvitedGuestEmails.Any())
                externalMeeting.Guests = string.Join(", ", externalMeetingDto.InvitedGuestEmails);

            List<ExternalMeetingTimeManagement> externalMeetingTimeManagements = null;

            // Use switch statemet to set the meeting type
            switch (externalMeetingDto.ExternalMeetingType)
            {
                case ExternalMeetingType.OneOnOne:
                    if (externalMeetingDto.InvitedGuestEmails.Any())
                        externalMeeting.Guests = string.Join(", ", externalMeetingDto.InvitedGuestEmails);

                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForOneOnOne(externalMeetingDto, externalMeeting, personalSchs);
                    Db.ExternalMeetingTimeManagements.AddRange(externalMeetingTimeManagements);

                    // Add the external meeting
                    Db.ExternalMeeting.Add(externalMeeting);

                    break;
                case ExternalMeetingType.RoundRobin:
                    if (externalMeetingDto.OtherMeetingHostsIds.Any())
                        externalMeeting.OtherMeetingHostsIds = string.Join(",", externalMeetingDto.OtherMeetingHostsIds);

                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForRoundRobin(externalMeetingDto, externalMeeting, personalSchs, externalMeetingDto.OtherMeetingHostsIds);
                    Db.ExternalMeetingTimeManagements.AddRange(externalMeetingTimeManagements);

                    // Add the external meeting
                    Db.ExternalMeeting.Add(externalMeeting);
                    break;
                case ExternalMeetingType.Group:
                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForGroup(externalMeetingDto, externalMeeting, personalSchs);
                    Db.ExternalMeetingTimeManagements.AddRange(externalMeetingTimeManagements);

                    Db.ExternalMeeting.Add(externalMeeting);
                    break;
                default:
                    break;
            }

            var invitedMmemberEmails = new List<string>();
            var invitedMmemberNames = new List<string>();
            var invitedUsers = new List<UserProfile>();

            // Add members to the database
            if (externalMeetingDto.TeamMembersIds.Any() && externalMeetingDto.ExternalMeetingType == ExternalMeetingType.Group)
            {
                var internalMemberIds = externalMeetingDto.TeamMembersIds;
                var internalMembersToAdd = internalMemberIds.Select(m => new ExternalMeetingMembers
                {
                    UserId = m,
                    ExternalMeetingId = externalMeeting.Id.ToString()
                });

                Db.ExternalMeetingMembers.AddRange(internalMembersToAdd);

                foreach (var invitedUser in externalMeetingDto.TeamMembersIds)
                {
                    var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                    invitedUsers.Add(user);
                    invitedMmemberEmails.Add(user.Email);
                    invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                }
            }

            if (externalMeetingDto.InvitedGuestEmails.Any())
            {
                //Todo: Add a check for number of guest that can be invited

                var enternalMemberIds = externalMeetingDto.InvitedGuestEmails;
                var enternalMembersToAdd = enternalMemberIds.Select(email => new ExternalMeetingMembers
                {
                    Email = email,
                    ExternalMeetingId = externalMeeting.Id.ToString()
                });

                Db.ExternalMeetingMembers.AddRange(enternalMembersToAdd);
            }
            if (externalMeetingDto.ExternalMeetingQuestionSetting.CustomQuestionDtos.Any())
            {
                var customQuestions = new List<CustomQuestion>();
                foreach (var customQuestionDto in externalMeetingDto.ExternalMeetingQuestionSetting.CustomQuestionDtos)
                {
                    //var options = externalMeetingDto.Options.Any() ? String.Join(",", externalMeetingDto.Options.ToArray()) : null;
                    var customQuestion = new CustomQuestion()
                    {
                        Options = customQuestionDto.Options.CapitalizeFirstLetterOfEachWord(),
                        ExternalMeetingId = externalMeeting.Id,
                        Question = customQuestionDto.CustomQuestion.CapitalizeFirstLetterOfEachWord(),
                        QuestionType = customQuestionDto.QuestionType
                    };
                    customQuestions.Add(customQuestion);

                }
                Db.CustomQuestions.AddRange(customQuestions);
                externalMeeting.CustomQuestion = customQuestions;

            }


            Db.PersonalSchedule.UpdateRange(personalSchs);
            result = await Db.SaveChangesAsync();

            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/meeting_template_new.html");
            var templateForMeetings = File.ReadAllText(templatePath);
            if (result > 0)
            {
                SendNotifications(externalMeetingDto, invitee, inviteeEmail, externalMeeting, templateForMeetings);
                //BackgroundJob.Enqueue(() => SendNotifications(externalMeetingDto, invitee, inviteeEmail, externalMeeting, templateForMeetings));

                return externalMeeting;
            }

            return null;
        }
        #endregion

        #region Update External Meeting
        /// <summary>
        /// Update External Meeting
        /// </summary>
        /// <param name="externalMeetingId"></param>
        /// <param name="updateExternalMeeting"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdateExternalMeeting(string externalMeetingId, ExternalMeetingDto updateExternalMeeting)
        {
            // Get the external meeting to be updated
            var externalMeeting = await Db.ExternalMeeting
                .Where(x => x.Id.ToString() == externalMeetingId && x.ExternalMeetingType == updateExternalMeeting.ExternalMeetingType)
                .FirstOrDefaultAsync();
            if (externalMeeting == null) { throw new RecordNotFoundException("External meeting not found"); }

            // Check if the external meeting has been booked by at least one person
            var bookedExternalMeetings = await Db.BookedExternalMeeting
                .AnyAsync(x => x.ExternalMeetingId.ToString() == externalMeetingId && (x.SelectedDateAndTime < updateExternalMeeting.CanBookStartDate || x.SelectedDateAndTime > updateExternalMeeting.CanBookEndDate));
            //if (bookedExternalMeetings) { throw new OperationFailedException("Update is not allowed. Meeting has been booked by at least one person"); }

            // Check for permission
            await CheckPermission(externalMeeting.CreatedBy.ToString(), updateExternalMeeting.UserId);

            var result = 0;
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == updateExternalMeeting.UserId)
                .Select(x => new { f = x.FirstName, l = x.LastName, email = x.Email })
                .FirstOrDefault();
            if (inviteeDetails == null)
                throw new RecordNotFoundException("User not found");

            var invitee = inviteeDetails.f + " " + inviteeDetails.l;
            var inviteeEmail = inviteeDetails.email;
            var loggedInUser = inviteeDetails;

            var externalMeetingQuestions = await Db.ExternalMeetingQuestion.Where(x => x.Id == externalMeeting.ExternalMeetingQuestionId)
                .AsNoTracking().FirstOrDefaultAsync();
            if (externalMeetingQuestions == null) { throw new RecordNotFoundException("External meeting question not found"); }

            // Handle personal schedules based on schedule type
            List<PersonalSchedule> personalSchs = new List<PersonalSchedule>();

            if (updateExternalMeeting.PersonalSchedule == PersonalScheduleType.Custom && !updateExternalMeeting.PersonalScheduleDtos.Any())
                throw new DirtyFormException($"PersonalScheduleDtos is required when ScheduleName is Custom");

            if (updateExternalMeeting.PersonalScheduleDtos.Any())
            {
                if (updateExternalMeeting.PersonalSchedule != PersonalScheduleType.Custom)
                    throw new DirtyFormException($"Custom PersonalScheduleType is required when PersonalScheduleDtos are provided");

                // Set the ExternalMeetingId for all PersonalScheduleDtos
                updateExternalMeeting.PersonalScheduleDtos.ForEach(x =>
                {
                    if (x.Id == null)
                        x.MeetingId = externalMeeting.MeetingId;
                });

                var schedulesToBeAdded = updateExternalMeeting.PersonalScheduleDtos.Where(x => x.Id == null).ToList();
                if (schedulesToBeAdded.Any())
                    await AddPersonalSchedule(schedulesToBeAdded);

                var schedulesToBeUpdated = updateExternalMeeting.PersonalScheduleDtos.Where(x => x.Id != null).ToList();
                if (schedulesToBeUpdated.Any())
                {
                    // Update new personal schedules
                    var scheduleUpdated = await UpdatePersonalSchedule(schedulesToBeUpdated);
                    if (!scheduleUpdated)
                        throw new DirtyFormException("Custom schedule could not be updated, meeting update failed");
                }

                personalSchs = await Db.PersonalSchedule
                    .Where(x => x.ScheduleName == updateExternalMeeting.PersonalSchedule && x.UserId.ToString() == updateExternalMeeting.UserId).ToListAsync();
            }
            else
            {
                // For non-Custom schedule types, get existing personal schedules
                personalSchs = await Db.PersonalSchedule
                    .Where(x => x.ScheduleName == updateExternalMeeting.PersonalSchedule && x.UserId.ToString() == updateExternalMeeting.UserId).ToListAsync();
            }


            //Delete if the user wants to remove existing custom question
            var customQuestions = await Db.CustomQuestions.Where(x => x.ExternalMeetingId == externalMeeting.Id).ToListAsync();
            if (customQuestions.Any())
            {
                Db.CustomQuestions.RemoveRange(customQuestions);
                customQuestions.Clear();
            }
            else
            {
                customQuestions = new List<CustomQuestion>();
            }

            if (updateExternalMeeting.ExternalMeetingQuestionSetting.CustomQuestionRequired && updateExternalMeeting.ExternalMeetingQuestionSetting.CustomQuestionDtos.Any())
            {

                foreach (var customQuestionDto in updateExternalMeeting.ExternalMeetingQuestionSetting.CustomQuestionDtos)
                {
                    var customQuestion = new CustomQuestion()
                    {
                        Options = customQuestionDto.Options.CapitalizeFirstLetterOfEachWord(),
                        ExternalMeetingId = externalMeeting.Id,
                        Question = customQuestionDto.CustomQuestion.CapitalizeFirstLetterOfEachWord(),
                        QuestionType = customQuestionDto.QuestionType
                    };
                    customQuestions.Add(customQuestion);
                }

                await Db.CustomQuestions.AddRangeAsync(customQuestions);

            }
            // Payload validations
            await PayloadValidationsForExternalMeetings(updateExternalMeeting, personalSchs, Guid.Parse(externalMeetingId));

            List<ExternalMeetingTimeManagement> externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();
            if (updateExternalMeeting.MeetingDuration != externalMeeting.MeetingDuration)
            {
                // Update the time break down of the selected personla schedule
                //First delete the already existing records
                var existingExternalMeetingTimeManagements = await Db.ExternalMeetingTimeManagements
                    .Where(x => x.ExternalMeetingId.ToString() == externalMeetingId).ToListAsync();
                if (existingExternalMeetingTimeManagements.Any())
                    Db.ExternalMeetingTimeManagements.RemoveRange(existingExternalMeetingTimeManagements);

                if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForOneOnOne(updateExternalMeeting, externalMeeting, personalSchs);
                else if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForRoundRobin(updateExternalMeeting, externalMeeting, personalSchs, updateExternalMeeting.OtherMeetingHostsIds);
                else if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownsForGroup(updateExternalMeeting, externalMeeting, personalSchs);

                Db.ExternalMeetingTimeManagements.AddRange(externalMeetingTimeManagements);
            }
            else
            {
                if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownForOonOAndRRUpdate(externalMeetingId, updateExternalMeeting, externalMeeting, personalSchs);
                else if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownForOonOAndRRUpdate(externalMeetingId, updateExternalMeeting, externalMeeting, personalSchs, updateExternalMeeting.OtherMeetingHostsIds);
                else if (updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                    externalMeetingTimeManagements = await ComputeTimeBreakDownForGroupUpdate(externalMeetingId, updateExternalMeeting, externalMeeting, personalSchs);

                Db.ExternalMeetingTimeManagements.AddRange(externalMeetingTimeManagements);
            }

            // Map External Meeting Questions
            var externalMeetingQuestionsToUpdate = _mapper.Map<ExternalMeetingQuestion>(updateExternalMeeting.ExternalMeetingQuestionSetting);
            externalMeetingQuestionsToUpdate.Id = externalMeeting.ExternalMeetingQuestionId;
            Db.ExternalMeetingQuestion.Update(externalMeetingQuestionsToUpdate);

            // Map External Meeting
            var externalMeetingToUpdate = updateExternalMeeting.Map(externalMeeting);

            // Ensure the PersonalScheduleId is set correctly if we're using Custom schedule type
            if (updateExternalMeeting.PersonalSchedule == PersonalScheduleType.Custom && personalSchs.Any())
            {
                externalMeetingToUpdate.PersonalScheduleId = personalSchs.First().Id;
            }

            //rtc here
            var tenantId = await Dbo.Tenants.Where(x => x.Subdomain.ToLower() == updateExternalMeeting.SubDomain.ToLower())
               .Select(x => x.Id).FirstOrDefaultAsync();


            externalMeetingToUpdate.UpdatedAt = GetAdjustedDateTimeBasedOnTZNow();

            if (updateExternalMeeting.InvitedGuestEmails.Any())
                externalMeetingToUpdate.Guests = string.Join(", ", updateExternalMeeting.InvitedGuestEmails);

            // Use switch statement for the meeting types
            switch (updateExternalMeeting.ExternalMeetingType)
            {
                case ExternalMeetingType.OneOnOne:
                    if (updateExternalMeeting.InvitedGuestEmails.Any())
                        externalMeetingToUpdate.Guests = string.Join(", ", updateExternalMeeting.InvitedGuestEmails);

                    // Add the external meeting
                    Db.ExternalMeeting.Update(externalMeetingToUpdate);

                    break;
                case ExternalMeetingType.RoundRobin:
                    if (updateExternalMeeting.OtherMeetingHostsIds.Any())
                        externalMeetingToUpdate.OtherMeetingHostsIds = string.Join(", ", updateExternalMeeting.OtherMeetingHostsIds);

                    // Add the external meeting
                    Db.ExternalMeeting.Update(externalMeetingToUpdate);
                    break;
                case ExternalMeetingType.Group:
                    // Add the external meeting
                    Db.ExternalMeeting.Update(externalMeetingToUpdate);
                    break;
                default:
                    break;
            }


            var invitedMmemberEmails = new List<string>();
            var invitedMmemberNames = new List<string>();
            var invitedUsers = new List<UserProfile>();
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/meeting_template.html");
            var templateFromFolder = File.ReadAllText(templatePath);

            // Update members to the database
            if (updateExternalMeeting.TeamMembersIds.Any() && updateExternalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                var internalMemberIds = updateExternalMeeting.TeamMembersIds;
                var existingInternalMembers = await Db.ExternalMeetingMembers.Where(x => internalMemberIds.Contains(x.UserId)).ToListAsync();
                if (existingInternalMembers.Any())
                    Db.ExternalMeetingMembers.RemoveRange(existingInternalMembers);

                var internalMembersToAdd = internalMemberIds.Select(m => new ExternalMeetingMembers
                {
                    UserId = m,
                    ExternalMeetingId = externalMeeting.Id.ToString()
                });

                Db.ExternalMeetingMembers.AddRange(internalMembersToAdd);

                foreach (var invitedUser in updateExternalMeeting.TeamMembersIds)
                {
                    var user = await Db.UserProfiles.Where(x => x.UserId == invitedUser).FirstOrDefaultAsync();
                    invitedUsers.Add(user);
                    invitedMmemberEmails.Add(user.Email);
                    invitedMmemberNames.Add(user.FirstName + " " + user.LastName);
                }
            }

            if (updateExternalMeeting.InvitedGuestEmails.Any())
            {
                var enternalMemberIds = updateExternalMeeting.InvitedGuestEmails;
                var existingEnternalMembers = await Db.ExternalMeetingMembers.Where(x => enternalMemberIds.Contains(x.UserId)).ToListAsync();
                if (existingEnternalMembers.Any())
                    Db.ExternalMeetingMembers.RemoveRange(existingEnternalMembers);

                var enternalMembersToAdd = enternalMemberIds.Select(m => new ExternalMeetingMembers
                {
                    UserId = m,
                    ExternalMeetingId = externalMeeting.Id.ToString()
                });

                Db.ExternalMeetingMembers.AddRange(enternalMembersToAdd);
            }

            result = await Db.SaveChangesAsync();

            if (result <= 0)
            {
                return false;
            }

            // Get booked external meeting members and send out updated mail
            var bookedExternalMeetingMembers = await Db.BookedExternalMeeting.Where(x => x.ExternalMeetingId == externalMeeting.Id).ToListAsync();
            // Todo

            var calenderMeeting = new CalenderMeeting
            {
                Id = externalMeeting.Id,
                Location = externalMeeting.Location,
                MeetingDuration = externalMeeting.MeetingDuration.ToString(),
                StartDate = externalMeeting.MeetingStartDateRange.Value,
                MeetingLink = externalMeeting.MeetingLink,
                Name = externalMeeting.MeetingName,
                IsCancelled = true
            };

            var invitedGuests = Db.ExternalMeeting.Where(x => x.Id == externalMeeting.Id)
                    .Select(x => x.Guests).FirstOrDefault();

            var invitedGuestsEmails = new List<string>();
            if (updateExternalMeeting.InvitedGuestEmails.Any())
            {
                invitedGuestsEmails = invitedGuests.Split(",").ToList();
                var meetingType = "";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                    meetingType = "Group";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                    meetingType = "Round Robin";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
                    meetingType = "One On One";

                var templatePathForExternalMeeting = Path.Combine(_environment.WebRootPath, @"EmailTemplates/external-meeting-invitation.html");
                var templateFromFolderForExt = File.ReadAllText(templatePathForExternalMeeting);
                templateFromFolderForExt = templateFromFolderForExt.Replace("{meetingType}", meetingType).Replace("{invitee}", invitee).Replace("{url}", externalMeeting.BookingLink).Replace("{name}", externalMeeting.MeetingName);

                BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(templateFromFolderForExt, updateExternalMeeting.InvitedGuestEmails, $"Meeting Invitation - {externalMeeting.MeetingName}(Update)"));
            }

            // Send email notofications to other meeting hosts after the meeting creation for round robin
            var aiName = Utility.Constants.AI_DEFAULT_NAME;
            var aiImage = Utility.Constants.AI_DEFAULT_IMAGE;
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                var existingOtherMeetingHosts = externalMeetingToUpdate.OtherMeetingHostsIds;
                var otherMeetingHostsIds = new List<string>();
                if (!string.IsNullOrEmpty(existingOtherMeetingHosts))
                    otherMeetingHostsIds = existingOtherMeetingHosts.Split(",").ToList();

                var message = $"You have been added as a potential host to a Round Robin meeting by {invitee}";
                var otherMeetingHostsName = new List<string>();
                var otherMeetingHosts = await Db.UserProfiles.Where(x => updateExternalMeeting.OtherMeetingHostsIds.Contains(x.UserId))
                    .ToListAsync();

                foreach (var host in otherMeetingHosts)
                    otherMeetingHostsName.Add(host.FirstName + " " + host.LastName);

                otherMeetingHostsName.AddRange(updateExternalMeeting.InvitedGuestEmails);

                SendMailToInternalMembers(updateExternalMeeting.SubDomain, invitee, inviteeEmail, calenderMeeting, templateFromFolder, otherMeetingHostsName, otherMeetingHosts, true, $"Meeting Invitation - {externalMeeting.MeetingName}(Update)", message, aiName, aiImage);
            }

            // Send email updates to team members after the meeting creation for group meeting
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                var teamMembersIds = await Db.ExternalMeetingMembers.Where(mem => mem.ExternalMeetingId == externalMeetingId)
                    .Select(x => x.UserId).ToArrayAsync();

                // Get team members emails from user profile using their Ids
                var teamMembersEmails = await Db.UserProfiles.Where(x => teamMembersIds.Contains(x.UserId))
                    .Select(x => x.Email).ToListAsync();
                teamMembersEmails.AddRange(updateExternalMeeting.InvitedGuestEmails);

                var inviteeEmails = new List<string>();
                var message = string.Format("{0} has invited you to a meeting.", invitee);

                SendMailToInternalMembers(updateExternalMeeting.SubDomain, invitee, inviteeEmail, calenderMeeting, templateFromFolder, invitedMmemberNames, invitedUsers, true, $"Meeting Invitation - {externalMeeting.MeetingName}(Group Meeting Update)", message, aiName, aiImage);
            }

            return true;
        }
        #endregion

        #region Get External Meeting By Id
        /// <summary>
        /// Get External Meeting By Id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="subdomain"></param>
        /// <returns cref="ExternalMeeting"></returns>
        public async Task<GenericResponse> GetExternalMeetingById(string id, string subdomain)
        {
            Tenant.Model.Tenant companyDetails = null;
            var externalMeeting = await Db.ExternalMeeting
                .Include(x => x.ExternalMeetingQuestion)
                .Include(x => x.CustomQuestion)
                .Include(x => x.PersonalSchedule)
                .FirstOrDefaultAsync(x => x.Id.ToString() == id);
            if (externalMeeting is null)
                throw new RecordNotFoundException("External meeting does not exist");

            // Get the user that created the meeting details using externalMeeting CreatedBy
            var createdBy = await Db.UserProfiles.Where(x => x.UserId == externalMeeting.CreatedBy.ToString())
                .Select(u => new UserDto
                {
                    Id = u.UserId.ToString(),
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    PhoneNumber = u.PhoneNumber,
                    ProfileUrl = u.ProfilePictureUrl
                }).FirstOrDefaultAsync();

            if (createdBy.ProfileUrl is not null)
            {
                var profileSignedUrl = await _aWSS3Sevices.GetSignedUrlAsync(createdBy.ProfileUrl);
                createdBy.ProfileUrl = profileSignedUrl;
            }

            // Get Company Details
            if (!string.IsNullOrEmpty(subdomain))
            {
                var tenant = await Dbo.Tenants.Where(x => x.Subdomain.ToLower() == subdomain.ToLower()).FirstOrDefaultAsync();
                if (tenant != null && !string.IsNullOrEmpty(tenant.LogoUrl))
                {
                    tenant.LogoUrl = await _aWSS3Sevices.GetSignedUrlAsync(tenant.LogoUrl);
                }

                createdBy.CompanyDetails = _mapper.Map<TenantDetailsVM>(tenant);
            }

            // Get all personal schedules attached to this external meeting
            var personalSchedules = await Db.PersonalSchedule
                .Where(x => x.MeetingId == externalMeeting.MeetingId)
                .ToListAsync();

            // Get the personal schedule details using externalMeeting personal schedule name
            var externalMeetingTimeBreakDowns = await Db.ExternalMeetingTimeManagements
                .Where(x => x.UserId == externalMeeting.CreatedBy.ToString() && x.ScheduleName == externalMeeting.PersonalSchedule.ScheduleName.ToString() && x.ExternalMeetingId == externalMeeting.Id).ToListAsync();

            // Ensure that the time break down dates are not in the past. Remove them if they are
            if (externalMeetingTimeBreakDowns != null && externalMeetingTimeBreakDowns.Any())
            {
                var nowUtc = DateTime.UtcNow;
                var currentDate = nowUtc.Date;
                var currentTime = nowUtc.TimeOfDay;

                foreach (var timeBreakDown in externalMeetingTimeBreakDowns.ToList())
                {
                    if (timeBreakDown.Date != null && timeBreakDown.Date < currentDate)
                    {
                        externalMeetingTimeBreakDowns.Remove(timeBreakDown);
                        continue;
                    }

                    // If the date is today, remove past time slots
                    var timeBreakDowns = timeBreakDown.TimeBreakDown.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
                    if (timeBreakDown.Date != null && timeBreakDown.Date == currentDate)
                    {
                        // Remove all time entries that have already passed
                        timeBreakDowns = timeBreakDowns
                            .Where(x =>
                            {
                                if (TimeSpan.TryParse(x, out var time))
                                    return time >= currentTime;
                                return false;
                            })
                            .ToList();

                        if (!timeBreakDowns.Any())
                        {
                            externalMeetingTimeBreakDowns.Remove(timeBreakDown);
                            continue;
                        }
                        timeBreakDown.TimeBreakDown = string.Join(",", timeBreakDowns);
                    }

                    if (externalMeeting.AvoidConflicts)
                    {
                        var freeTimes = new List<string>();

                        // Get all externalMeetingId that belongs to the creator of the external meeting
                        var externalMeetingIds = await Db.ExternalMeeting
                            .Where(x => x.CreatedBy.ToString() == externalMeeting.CreatedBy.ToString() && x.MeetingEndDateRange >= DateTime.UtcNow)
                            .Select(x => x.Id)
                            .ToListAsync();

                        foreach (var time in timeBreakDowns)
                        {
                            var timeSpan = TimeSpan.Parse(time);
                            var startDateTime = new DateTime(
                                timeBreakDown.Date.Value.Year,
                                timeBreakDown.Date.Value.Month,
                                timeBreakDown.Date.Value.Day,
                                timeSpan.Hours,
                                timeSpan.Minutes,
                                0);

                            var endDateTime = startDateTime.AddMinutes(externalMeeting.MeetingDuration); // For the current schedulling operation

                            var hasOverlap = await Db.BookedExternalMeeting
                                .AnyAsync(x =>
                                    externalMeetingIds.Contains(x.ExternalMeetingId) &&
                                    !x.IsCancelled &&
                                    x.SelectedDateAndTime < endDateTime &&
                                    x.SelectedDateAndTime.AddMinutes(x.DurationInMinutes) > startDateTime);

                            if (!hasOverlap)
                                freeTimes.Add(time);
                        }

                        // Update with only free time slots
                        timeBreakDown.TimeBreakDown = string.Join(",", freeTimes);
                    }
                    else
                    {
                        if (externalMeeting.MaxInvitePerMeeting != null && externalMeeting.MaxInvitePerMeeting > 0)
                        {
                            // Get the count of booked external meetings for each time in the time break down
                            foreach (var time in timeBreakDowns)
                            {
                                var timeSpan = TimeSpan.Parse(time);
                                var startDateTime = new DateTime(
                                timeBreakDown.Date.Value.Year,
                                timeBreakDown.Date.Value.Month,
                                timeBreakDown.Date.Value.Day,
                                timeSpan.Hours, timeSpan.Minutes, 0);

                                var endDateTime = startDateTime.AddMinutes(externalMeeting.MeetingDuration);
                                var bookedCount = await Db.BookedExternalMeeting
                                    .CountAsync(x => x.ExternalMeetingId == externalMeeting.Id && !x.IsCancelled && x.SelectedDateAndTime >= startDateTime && x.SelectedDateAndTime < endDateTime);

                                if (bookedCount >= externalMeeting.MaxInvitePerMeeting)
                                {
                                    // Remove this time slot if it has reached the max invite count
                                    timeBreakDowns.Remove(time);
                                }
                            }
                        }
                    }
                }
            }

            externalMeeting.ExternalMeetingTimeBreakDowns = externalMeetingTimeBreakDowns;

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                // get company infomations
                var tenantId = await Dbo.UserCompanies.Where(usr => usr.UserId == externalMeeting.CreatedBy.ToString())
                    .Select(x => x.TenantId).FirstOrDefaultAsync();
                companyDetails = await Dbo.Tenants.FirstOrDefaultAsync(t => t.Id == tenantId);
                companyDetails.LogoUrl = await _aWSS3Sevices.GetSignedUrlAsync(companyDetails?.LogoUrl);
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "External Meetings retrieved successfuly",
                Data = new
                {
                    ExternalMeeting = externalMeeting,
                    CreatedBy = createdBy,
                    CompanyDetails = companyDetails,
                    PersonalSchedules = personalSchedules
                }
            };
        }
        #endregion

        #region Lock or Unlock Selected Date For External Booking
        /// <summary>
        /// Lock or Unlock Selected Date For External Booking
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> LockOrUnlockSelectedDate(LockOrUnlockSelectedDateDto model)
        {
            var timeBreakDown = await Db.ExternalMeetingTimeManagements
                .Where(x => x.Id.ToString() == model.TimeBreakDownId && x.ExternalMeetingId.ToString() == model.ExternalMeetingId)
                .FirstOrDefaultAsync();
            if (timeBreakDown == null) return new GenericResponse { ResponseCode = "404", ResponseMessage = "Time break down not found" };

            timeBreakDown.IsLockedTill = model.Lock ? DateTime.UtcNow.AddMinutes(10) : null;
            Db.ExternalMeetingTimeManagements.Update(timeBreakDown);
            var res = await Db.SaveChangesAsync();

            if (res > 0)
            {
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = model.Lock ? "Selected date locked successfully" : "Selected date unlocked successfully"
                };
            }

            return new GenericResponse { ResponseCode = "500", ResponseMessage = "Unable to lock or unlock selected date" };
        }
        #endregion

        #region Book External Meeting
        /// <summary>
        /// Book External Meeting
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="DirtyFormException"></exception>
        public async Task<BookedExternalMeeting> BookExternalMeetingorEvent(BookExternalMeetingDto model)
        {
            model.FullName = model.FullName.CapitalizeFirstLetterOfEachWord();
            model.Email = model.Email.ToLower();

            // Check if the supplied external meeting exists
            var externalMeeting = Db.ExternalMeeting
                .Include(x => x.ExternalMeetingQuestion).FirstOrDefault(x => x.Id == model.ExternalMeetingId);
            if (externalMeeting == null) { throw new RecordNotFoundException("External meeting no longer exist. This meeting has been deleted by the creator/organization"); }

            PayloadValidations(externalMeeting, model);

            // Get the user that created the meeting details using externalMeeting CreatedBy
            var meetingOwner = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == externalMeeting.CreatedBy.ToString());

            var bookedExternalMeet = _mapper.Map<BookedExternalMeeting>(model);
            if (model.GuestEmails.Any())
                bookedExternalMeet.GuestEmails = string.Join(",", model.GuestEmails);

            bookedExternalMeet.CancelMeetingLink = string.Format(Utility.Constants.JOBLE_FE_URL + "/booking/cancel/{1}/{2}", model.SubDomain, bookedExternalMeet.Id, model.Email);
            bookedExternalMeet.ReScheduleMeetingLink = string.Format(Utility.Constants.JOBLE_FE_URL + "/booking/reschedule/{1}/{2}", model.SubDomain, bookedExternalMeet.Id, model.Email);

            // Update the personal schedule of the user
            var scheduleName = Db.PersonalSchedule
                .Where(x => x.UserId == externalMeeting.CreatedBy && x.Id == externalMeeting.PersonalScheduleId)
                .Select(y => y.ScheduleName).FirstOrDefault();

            // Process the booking
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
            {
                ProcessBookingForOneOnOneOrRoundRobinMeeting(model.SelectedDateAndTime, externalMeeting.CreatedBy, bookedExternalMeet, scheduleName, externalMeeting);
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                await ChooseMeetingHost(model, externalMeeting, scheduleName);
                ProcessBookingForOneOnOneOrRoundRobinMeeting(model.SelectedDateAndTime, externalMeeting.CreatedBy, bookedExternalMeet, scheduleName, externalMeeting);
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                await ProcessBookingForGroupMeeting(model, externalMeeting, bookedExternalMeet, scheduleName);
            }

            Db.BookedExternalMeeting.Add(bookedExternalMeet);

            // Update the external member invite to accepted
            var externalMemberInvite = Db.ExternalMeetingMembers
                .FirstOrDefault(x => x.ExternalMeetingId == externalMeeting.Id.ToString() && x.Email == model.Email);
            if (externalMemberInvite != null)
            {
                externalMemberInvite.InviteResponse = InviteResponse.Yes;
                externalMemberInvite.SelectedDateAndTime = model.SelectedDateAndTime;
                Db.ExternalMeetingMembers.Update(externalMemberInvite);
            }

            if (model.CustomQuestionAnwsersDto.Any())
            {
                var answers = new List<CustomQuestionAnswer>();
                foreach (var answer in model.CustomQuestionAnwsersDto)
                {
                    var customAnswer = new CustomQuestionAnswer()
                    {
                        Answer = answer.Answer.CapitalizeFirstLetterOfFirstWord(),
                        Email = model.Email,
                        Question = answer.Question.CapitalizeFirstLetterOfEachWord(),
                        CustomQuestionId = Guid.Parse(answer.CustomQuestionId),
                        BookedExternalMeetingId = bookedExternalMeet.Id
                    };
                    answers.Add(customAnswer);
                }
                Db.CustomQuestionAnswers.AddRange(answers);
            }

            var res = await Db.SaveChangesAsync();

            if (res > 0)
            {
                // Send notification to the user
                var from = bookedExternalMeet.FullName ?? bookedExternalMeet.Email;
                var notification = new AddNotificationDto
                {
                    Message = $"You have one booking for {externalMeeting.MeetingName} meeting from {from}",
                    Event = EventCategory.Calender,
                    EventId = externalMeeting.Id.ToString(),
                    CreatedBy = Guid.NewGuid().ToString()
                };
                var notificationId = await AddNotification(notification);
                if (notificationId is not null)
                {
                    await AddUserNotification(new List<string> { externalMeeting.CreatedBy.ToString() }, Guid.Parse(notificationId));
                }

                await SendOutBookingNotifications(model, externalMeeting, meetingOwner, bookedExternalMeet, false);
            }

            return res > 0 ? bookedExternalMeet : null;
        }
        #endregion

        #region Update Booked External Meeting - By Joble user
        /// <summary>
        /// Reschedule Booked Meeting
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<GenericResponse> UpdateBookedMeeting(UpdateBookedExtMeetingDto model)
        {
            // Check that a booked meeting with bookedExternalMeetingId exits
            var bookedExternalMeeting = Db.BookedExternalMeeting.FirstOrDefault(x => x.Id.ToString() == model.BookedExternalMeetingId);
            if (bookedExternalMeeting == null) { throw new RecordNotFoundException("Meeting not found"); }

            // Check that the booked meeting has not been cancelled
            if (bookedExternalMeeting.IsCancelled)
                throw new OperationNotAllowedException("This meeting has been canceled");

            var oldSelectedTime = bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString();

            // Get the external meeing using the bookedExternalMeeting ExternalMeetingId
            var externalMeeting = Db.ExternalMeeting
                .Include(x => x.ExternalMeetingQuestion)
                .FirstOrDefault(x => x.Id == bookedExternalMeeting.ExternalMeetingId);

            if (externalMeeting.IsLocked)
                throw new OperationNotAllowedException("This meeting is locked");
            if (externalMeeting.IsCancelled)
                throw new OperationNotAllowedException("This meeting has been canceled");

            // Check that one of the internal members is not the same as the creator of the externalmeeting
            if (model.InternalMemberIds.Any(x => x == externalMeeting.CreatedBy.ToString()))
                throw new OperationNotAllowedException("You cannot add the meeting creator as a member");

            // Get the user that created the meeting details using externalMeeting CreatedBy
            var loggedInUser = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == externalMeeting.CreatedBy.ToString());

            bookedExternalMeeting.DurationInMinutes = externalMeeting.MeetingDuration;
            bookedExternalMeeting.SelectedDateAndTime = model.SelectedDateAndTime;
            Db.BookedExternalMeeting.Update(bookedExternalMeeting);

            // Send notification to the user
            if (loggedInUser.Id != externalMeeting.CreatedBy.ToString())
            {
                var from = bookedExternalMeeting.FullName ?? bookedExternalMeeting.Email;
                var notification = new AddNotificationDto
                {
                    Message = $"You have one booking update for {externalMeeting.MeetingName} meeting from {from}",
                    Event = EventCategory.Calender,
                    EventId = externalMeeting.Id.ToString(),
                    CreatedBy = Guid.NewGuid().ToString()
                };
                var notificationId = await AddNotification(notification);
                if (notificationId is not null)
                {
                    await AddUserNotification(new List<string> { externalMeeting.CreatedBy.ToString() }, Guid.Parse(notificationId));
                }
            }

            // Add members to the booked meetig
            var oldExternalMembers = await Db.BookedMeetingMembers
                .Where(x => x.BookedMeetingId == bookedExternalMeeting.Id.ToString())
                .ToListAsync();
            if (oldExternalMembers.Any())
                Db.BookedMeetingMembers.RemoveRange(oldExternalMembers);

            if (model.ExternalMemberEmails.Any())
            {
                foreach (var email in model.ExternalMemberEmails)
                {
                    var externalMember = new BookedMeetingMember
                    {
                        BookedMeetingId = bookedExternalMeeting.Id.ToString(),
                        Email = email
                    };

                    Db.BookedMeetingMembers.Add(externalMember);
                }
            }

            if (model.InternalMemberIds.Any())
            {
                foreach (var internalMemberId in model.InternalMemberIds)
                {
                    var internalMember = new BookedMeetingMember
                    {
                        BookedMeetingId = bookedExternalMeeting.Id.ToString(),
                        UserId = internalMemberId
                    };

                    Db.BookedMeetingMembers.Add(internalMember);
                }
            }

            var dbResult = await Db.SaveChangesAsync();
            if (dbResult > 0)
            {
                // Cancel the meeting background jobs
                var meetingJobIds = await Db.BackGroundJobIds.Where(x => x.EventId == bookedExternalMeeting.Id.ToString()).ToListAsync();
                foreach (var meetingJobId in meetingJobIds)
                {
                    BackgroundJob.Delete(meetingJobId.JobId);
                }

                var bookExternalMeetingDto = new BookExternalMeetingDto
                {
                    DurationInMinutes = model.DurationInMinutes,
                    Email = bookedExternalMeeting.Email,
                    FullName = bookedExternalMeeting.FullName,
                    SelectedDateAndTime = model.SelectedDateAndTime,
                    SubDomain = model.SubDomain,
                    GuestEmails = bookedExternalMeeting.GuestEmails.Split(",").ToList(),
                    ExternalMeetingId = bookedExternalMeeting.ExternalMeetingId,
                    ExternalMemberEmails = model.ExternalMemberEmails,
                    InternalMemberIds = model.InternalMemberIds,
                };

                await SendOutBookingNotifications(bookExternalMeetingDto, externalMeeting, loggedInUser, bookedExternalMeeting, true);
            }

            return new GenericResponse
            {
                ResponseCode = dbResult > 0 ? "200" : "500",
                ResponseMessage = dbResult > 0 ? "Updated successfully" : "Update failed",
                Data = dbResult > 0 ? true : false
            };
        }
        #endregion

        #region Cancel Booked External Meeting
        /// <summary>
        /// Cancel Booked External Meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="reason"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<string> CancelBookedExternalMeeting(string meetingId, string reason, string subdomain)
        {
            var bookedExternalMeeting = await Db.BookedExternalMeeting
                .FirstOrDefaultAsync(x => x.Id.ToString() == meetingId);
            if (bookedExternalMeeting == null) { throw new RecordNotFoundException("Meeting not found"); }

            if (bookedExternalMeeting.IsCancelled)
                return "You have already cancelled this booking";

            // Get the external meeing using the bookedExternalMeeting ExternalMeetingId
            var externalMeeting = await Db.ExternalMeeting.FirstOrDefaultAsync(x => x.Id == bookedExternalMeeting.ExternalMeetingId);
            if (externalMeeting == null) { throw new RecordNotFoundException("Meeting not found"); }

            var customAnswers = await Db.CustomQuestionAnswers.Where(x => x.BookedExternalMeetingId == bookedExternalMeeting.Id).ToListAsync();
            if (customAnswers.Any())
                Db.CustomQuestionAnswers.RemoveRange(customAnswers);

            var selectedMeetingTimeMgt = await Db.ExternalMeetingTimeManagements
                .Where(x => x.PersonalScheduleId.ToString() == bookedExternalMeeting.PersonalScheduleId && x.ExternalMeetingId == bookedExternalMeeting.ExternalMeetingId).FirstOrDefaultAsync();
            var personalSchTimeBreakDown = selectedMeetingTimeMgt?.TimeBreakDown?.Split(",").ToList();
            var selectedTimeSlots = selectedMeetingTimeMgt?.SelectedTimeSlots?.Split(",").ToList();

            // Update the personal schedule of the user if the meeting type is one on one
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne || externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                var selectedTime = bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString();
                var selectedDay = bookedExternalMeeting.SelectedDateAndTime.DayOfWeek.ToString();

                if (personalSchTimeBreakDown is not null)
                    personalSchTimeBreakDown.Add(selectedTime);
                else
                    personalSchTimeBreakDown = new List<string> { selectedTime };

                selectedTimeSlots?.Remove(selectedTime);
                if (personalSchTimeBreakDown.Any())
                    selectedMeetingTimeMgt.TimeBreakDown = string.Join(",", personalSchTimeBreakDown.Distinct());

                if (selectedTimeSlots != null && selectedTimeSlots.Any())
                    selectedMeetingTimeMgt.SelectedTimeSlots = string.Join(",", selectedTimeSlots.Distinct());
                else
                    selectedMeetingTimeMgt.SelectedTimeSlots = null;
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                if (selectedMeetingTimeMgt.SelectedTimeSlots.Contains(bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString()))
                {
                    personalSchTimeBreakDown.Add(bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString());
                    selectedTimeSlots.Remove(bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString());
                }
            }



            Db.ExternalMeetingTimeManagements.Update(selectedMeetingTimeMgt);
            bookedExternalMeeting.IsCancelled = true;
            bookedExternalMeeting.ReasonForCancelling = reason;

            Db.BookedExternalMeeting.Update(bookedExternalMeeting);
            var dbResult = await Db.SaveChangesAsync();

            if (dbResult > 0)
            {
                // Send notification to the user
                var from = bookedExternalMeeting.FullName ?? bookedExternalMeeting.Email;
                var notification = new AddNotificationDto
                {
                    Message = $"{from} cancelled the booking for {externalMeeting.MeetingName} meeting",
                    Event = EventCategory.Calender,
                    EventId = externalMeeting.Id.ToString(),
                    CreatedBy = Guid.NewGuid().ToString()
                };
                var notificationId = await AddNotification(notification);
                if (notificationId is not null)
                {
                    await AddUserNotification(new List<string> { externalMeeting.CreatedBy.ToString() }, Guid.Parse(notificationId));
                }

                // Send a mail comfirming their cancllation
                var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/meeting_template_new.html");
                var template = File.ReadAllText(templatePath);
                var message = "This is a confirmation that this event has been cancelled.";
                var meetingOwner = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == externalMeeting.CreatedBy.ToString());
                var otherInvitedTeamEmails = await Db.ExternalMeetingMembers
                        .Where(x => x.ExternalMeetingId == externalMeeting.Id.ToString() &&
                        (x.InviteResponse == InviteResponse.Yes || x.InviteResponse == InviteResponse.Maybe) && x.SelectedDateAndTime == bookedExternalMeeting.SelectedDateAndTime)
                        .Select(x => x.Email).ToListAsync();

                if (otherInvitedTeamEmails == null)
                    otherInvitedTeamEmails = new List<string>();

                otherInvitedTeamEmails.Add(bookedExternalMeeting.Email);
                var invitedTeamMemberNames = new List<string>();
                var invitedUserProfiles = new List<UserProfile>();
                var calenderMeeting = new CalenderMeeting
                {
                    Id = externalMeeting.Id,
                    Location = externalMeeting.Location,
                    MeetingDuration = externalMeeting.MeetingDuration.ToString(),
                    StartDate = bookedExternalMeeting.SelectedDateAndTime,
                    MeetingLink = externalMeeting.MeetingLink,
                    Name = externalMeeting.MeetingName
                };

                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                {
                    otherInvitedTeamEmails?.AddRange(bookedExternalMeeting.GuestEmails.Split(",").ToList());
                    var otherMeetingHostIds = externalMeeting.OtherMeetingHostsIds.Split(",");
                    foreach (var hostId in otherMeetingHostIds)
                    {
                        var host = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == hostId);
                        invitedUserProfiles.Add(host);
                        invitedTeamMemberNames.Add(host.FirstName + " " + host.LastName);
                    }
                }

                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                {
                    var otherInvitedTeamMemberIds = await Db.ExternalMeetingMembers
                        .Where(x => x.ExternalMeetingId == externalMeeting.Id.ToString() &&
                        (x.InviteResponse == InviteResponse.Yes || x.InviteResponse == InviteResponse.Maybe) && x.SelectedDateAndTime == bookedExternalMeeting.SelectedDateAndTime)
                        .Select(x => x.UserId).ToListAsync();

                    foreach (var memberId in otherInvitedTeamMemberIds)
                    {
                        var member = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == memberId);
                        invitedUserProfiles.Add(member);
                        invitedTeamMemberNames.Add(member.FirstName + " " + member.LastName);
                    }
                }

                var aiName = Utility.Constants.AI_DEFAULT_NAME;
                var aiImage = Utility.Constants.AI_DEFAULT_IMAGE;
                invitedTeamMemberNames.AddRange(otherInvitedTeamEmails);
                SendMailToExternalMembers(subdomain, bookedExternalMeeting.FullName, meetingOwner.Email, calenderMeeting, template, invitedTeamMemberNames, false, otherInvitedTeamEmails, $"Cancelled Meeting: Meeting Invitation({externalMeeting.MeetingName})", message, aiName, aiImage, meetingOwner.FirstName + " " + meetingOwner.LastName);

                SendMailToInternalMembers(subdomain, meetingOwner.FirstName + " " + meetingOwner.LastName, meetingOwner.Email, calenderMeeting, template, invitedTeamMemberNames, invitedUserProfiles, false, $"Cancelled Event: Meeting Invitation({externalMeeting.MeetingName})", message, aiName, aiImage);

                // Cancel the meeting background jobs
                var meetingJobIds = await Db.BackGroundJobIds.Where(x => x.EventId == bookedExternalMeeting.Id.ToString()).ToListAsync();
                foreach (var meetingJobId in meetingJobIds)
                {
                    BackgroundJob.Delete(meetingJobId.JobId);
                }

                var bookedMeetingNotofication = new BookedMeetingNotificationDto
                {
                    Subdomain = subdomain,
                    FullName = bookedExternalMeeting.FullName,
                    Email = bookedExternalMeeting.Email,
                    GuestEmails = bookedExternalMeeting.GuestEmails.Split(",").ToList(),
                    ExternalMeeting = externalMeeting,
                    MeetingOwner = meetingOwner,
                    Subject = $"Cancelled Event: {externalMeeting.MeetingName}",
                    BookedMeetingSubject = $"Cancelled Event: {externalMeeting.MeetingName}"
                };

                // BackgroundJob.Enqueue(() => SendOutNotificationForBookedMeeting(bookedMeetingNotofication));
            }

            return dbResult > 0 ? "Success" : "Failure";
        }
        #endregion

        #region ReSchedule Booked Meeting
        /// <summary>
        /// Reschedule Booked Meeting
        /// </summary>
        /// <param name="bookedExternalMeetingId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> ReScheduleBookedMeeting(string bookedExternalMeetingId, BookExternalMeetingDto model)
        {
            // Check that a booked meeting with bookedExternalMeetingId exits
            var bookedExternalMeeting = Db.BookedExternalMeeting.FirstOrDefault(x => x.Id.ToString() == bookedExternalMeetingId);
            if (bookedExternalMeeting == null) { throw new RecordNotFoundException("Meeting not found"); }

            var oldSelectedTime = bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString();

            // Get the external meeing using the bookedExternalMeeting ExternalMeetingId
            var externalMeeting = Db.ExternalMeeting
                .Include(x => x.ExternalMeetingQuestion)
                .FirstOrDefault(x => x.Id == bookedExternalMeeting.ExternalMeetingId);

            PayloadValidations(externalMeeting, model);

            // Get the user that created the meeting details using externalMeeting CreatedBy
            var loggedInUser = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == externalMeeting.CreatedBy.ToString());

            // Map the model to the bookedExternalMeeting
            var bookedExternalMeetingMapped = _mapper.Map(model, bookedExternalMeeting);
            bookedExternalMeetingMapped.BookedOn = bookedExternalMeeting.BookedOn;
            bookedExternalMeetingMapped.ReScheduledOn = GetAdjustedDateTimeBasedOnTZNow();
            bookedExternalMeetingMapped.Id = bookedExternalMeeting.Id;
            Db.BookedExternalMeeting.Update(bookedExternalMeetingMapped);

            var scheduleName = Db.PersonalSchedule
              .Where(x => x.UserId == externalMeeting.CreatedBy && x.Id == externalMeeting.PersonalScheduleId)
              .Select(y => y.ScheduleName).FirstOrDefault();

            // Update the personal schedule of the user if the meeting type is one on one
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
            {
                var selectedMeetingTimeMgt = await Db.ExternalMeetingTimeManagements
                    .Where(x => x.PersonalScheduleId.ToString() == bookedExternalMeeting.PersonalScheduleId && x.ExternalMeetingId == bookedExternalMeeting.ExternalMeetingId)
                    .FirstOrDefaultAsync();

                var selectedTime = bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString();
                var selectedDay = bookedExternalMeeting.SelectedDateAndTime.DayOfWeek.ToString();

                var personalSchTimeBreakDown = selectedMeetingTimeMgt?.TimeBreakDown?.Split(",").ToList();
                var selectedTimeSlots = selectedMeetingTimeMgt?.SelectedTimeSlots?.Split(",").ToList();
                if (personalSchTimeBreakDown is not null && !personalSchTimeBreakDown.Contains(oldSelectedTime))
                    personalSchTimeBreakDown.Add(oldSelectedTime);
                else
                    personalSchTimeBreakDown = new List<string> { oldSelectedTime };

                selectedTimeSlots?.Remove(oldSelectedTime);

                if (personalSchTimeBreakDown.Any())
                    selectedMeetingTimeMgt.TimeBreakDown = string.Join(",", personalSchTimeBreakDown.Distinct());

                Db.ExternalMeetingTimeManagements.Update(selectedMeetingTimeMgt);
                //var result = await this.Db.SaveChangesAsync();

                ProcessBookingForOneOnOneOrRoundRobinMeeting(model.SelectedDateAndTime, externalMeeting.CreatedBy, bookedExternalMeetingMapped, scheduleName, externalMeeting);
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                await ChooseMeetingHost(model, externalMeeting, scheduleName);

                var selectedMeetingTimeMgt = await Db.ExternalMeetingTimeManagements
                                   .Where(x => x.PersonalScheduleId.ToString() == bookedExternalMeeting.PersonalScheduleId && x.ExternalMeetingId == bookedExternalMeeting.ExternalMeetingId)
                                   .FirstOrDefaultAsync();

                var selectedTime = bookedExternalMeeting.SelectedDateAndTime.ToShortTimeString();
                var selectedDay = bookedExternalMeeting.SelectedDateAndTime.DayOfWeek.ToString();

                var personalSchTimeBreakDown = selectedMeetingTimeMgt?.TimeBreakDown?.Split(",").ToList();
                var selectedTimeSlots = selectedMeetingTimeMgt?.SelectedTimeSlots?.Split(",").ToList();
                if (personalSchTimeBreakDown is not null && !personalSchTimeBreakDown.Contains(oldSelectedTime))
                    personalSchTimeBreakDown.Add(oldSelectedTime);
                else
                    personalSchTimeBreakDown = new List<string> { oldSelectedTime };

                selectedTimeSlots?.Remove(oldSelectedTime);

                if (personalSchTimeBreakDown.Any())
                    selectedMeetingTimeMgt.TimeBreakDown = string.Join(",", personalSchTimeBreakDown.Distinct());

                Db.ExternalMeetingTimeManagements.Update(selectedMeetingTimeMgt);

                ProcessBookingForOneOnOneOrRoundRobinMeeting(model.SelectedDateAndTime, externalMeeting.CreatedBy, bookedExternalMeetingMapped, scheduleName, externalMeeting);
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                await ProcessBookingForGroupMeeting(model, externalMeeting, bookedExternalMeetingMapped, scheduleName, oldSelectedTime);
            }

            // Update the external member invite to accepted
            var externalMemberInvite = Db.ExternalMeetingMembers
                .FirstOrDefault(x => x.ExternalMeetingId == externalMeeting.Id.ToString() && x.Email == model.Email);
            if (externalMemberInvite != null)
            {
                externalMemberInvite.InviteResponse = InviteResponse.Yes;
                externalMemberInvite.SelectedDateAndTime = model.SelectedDateAndTime;
                Db.ExternalMeetingMembers.Update(externalMemberInvite);
            }

            // Send notification to the user
            var from = bookedExternalMeetingMapped.FullName ?? bookedExternalMeetingMapped.Email;
            var notification = new AddNotificationDto
            {
                Message = $"You have one booking update for {externalMeeting.MeetingName} meeting from {from}",
                Event = EventCategory.Calender,
                EventId = externalMeeting.Id.ToString(),
                CreatedBy = Guid.NewGuid().ToString()
            };
            var notificationId = await AddNotification(notification);
            if (notificationId is not null)
            {
                await AddUserNotification(new List<string> { externalMeeting.CreatedBy.ToString() }, Guid.Parse(notificationId));
            }

            var dbResult = await Db.SaveChangesAsync();
            if (dbResult > 0)
            {
                // Cancel the meeting background jobs
                var meetingJobIds = await Db.BackGroundJobIds.Where(x => x.EventId == bookedExternalMeeting.Id.ToString()).ToListAsync();
                foreach (var meetingJobId in meetingJobIds)
                {
                    BackgroundJob.Delete(meetingJobId.JobId);
                }

                await SendOutBookingNotifications(model, externalMeeting, loggedInUser, bookedExternalMeetingMapped, true);
            }

            return dbResult > 0;
        }
        #endregion

        #region Get External Meeting/Event By UserId
        /// <summary>
        /// Get External Meeting/Event By UserId
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetExternalMeetingOrEventByUserId(string userId)
        {
            var externamMemberIds = await Db.ExternalMeetingMembers.Where(x => x.UserId == userId).Select(m => m.ExternalMeetingId).ToListAsync();
            var externalMeetings = await Db.ExternalMeeting
                .Include(x => x.ExternalMeetingQuestion)
                .Include(x => x.CustomQuestion)
                .Where(x => x.CreatedBy.ToString() == userId || externamMemberIds.Contains(x.Id.ToString())).ToListAsync();
            if (!externalMeetings.Any()) throw new RecordNotFoundException("No External meeting found for the user");

            // Get the personal schedule of the external meeting
            var personalSchs = new List<PersonalSchedule>();
            foreach (var externalMeet in externalMeetings)
            {
                var personalSchName = Db.PersonalSchedule.Where(x => x.Id == externalMeet.PersonalScheduleId)
                    .Select(x => x.ScheduleName).FirstOrDefault();
                var personalSch = Db.PersonalSchedule.Where(x => x.ScheduleName == personalSchName).ToList();
                personalSchs.AddRange(personalSch);
            }

            return new GenericResponse
            {
                ResponseMessage = "External Meetings retrived successfully",
                ResponseCode = "200",
                Data = new
                {
                    ExternalMeetings = externalMeetings,
                    PersonalSchedules = personalSchs
                }
            };
        }
        #endregion

        #region Get Booked External Meeting/Event By externalmeetingId
        public async Task<List<BookedExternalMeeting>> GetBookedExternalMeetingOrEventByMeetingId(string meetingId)
        {
            return await Db.BookedExternalMeeting
                .Where(x => x.ExternalMeetingId.ToString() == meetingId && x.IsCancelled == false)
                .Include(x => x.CustomQuestionAnswer).ToListAsync();
        }
        #endregion

        #region Get Booked External Meeting by Id
        public async Task<BookedExternalMeeting> GetBookedExternalMeetingById(string bookedExternalMeetingId)
        {
            var bookedExternalMeeting = await Db.BookedExternalMeeting
                .Include(x => x.ExternalMeeting)
                .Include(x => x.CustomQuestionAnswer)
                .Where(x => x.Id.ToString() == bookedExternalMeetingId).FirstOrDefaultAsync();

            if (bookedExternalMeeting == null) return null;

            var meetingMembers = Db.BookedMeetingMembers.Where(x => x.BookedMeetingId == bookedExternalMeeting.Id.ToString()).ToList();
            var meetingOwner = Db.UserProfiles
                .Where(x => x.UserId == bookedExternalMeeting.ExternalMeeting.CreatedBy.ToString())
                .Select(x => new UserMDVm
                {
                    FirstName = x.FirstName,
                    LastName = x.LastName,
                    Email = x.Email,
                    ProfilePictureUrl = x.ProfilePictureUrl,
                    Id = x.UserId
                }).FirstOrDefault();

            if (meetingOwner.ProfilePictureUrl is not null)
                meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);
            bookedExternalMeeting.MeetingOwner = meetingOwner;

            var members = meetingMembers.Select(x =>
            {
                var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                if (user != null)
                {
                    return new UserMDVm
                    {
                        Id = user.Id,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                    };
                }

                return new UserMDVm();
            }).ToList();
            bookedExternalMeeting.InternalMembers = members.Where(m => m.Id != null || m.Email != null).ToList();

            bookedExternalMeeting.ExternalMembers = meetingMembers
                .Where(x => x.Email != null && x.UserId == null)
                .Select(x => x.Email).ToList();

            return bookedExternalMeeting;
        }
        #endregion

        #region Get Booked External Meetings By UserId
        public async Task<List<BookedExternalMeeting>> GetBookedExternalMeetingByUserId(string userId)
        {
            var externalMeetingIds = await Db.ExternalMeeting
                .Where(x => x.CreatedBy.ToString() == userId).Select(m => m.Id.ToString()).ToListAsync();
            externalMeetingIds.AddRange(await Db.ExternalMeetingMembers
                .Where(x => x.UserId == userId).Select(m => m.ExternalMeetingId).ToListAsync());

            externalMeetingIds = externalMeetingIds.Distinct().ToList();
            var bookedExternalMeetings = await Db.BookedExternalMeeting
                .Include(x => x.ExternalMeeting)
                .Include(x => x.CustomQuestionAnswer)
                .Where(x => externalMeetingIds.Contains(x.ExternalMeetingId.ToString()) && !x.IsCancelled).ToListAsync();

            // Get bookedExtrernalMeetingIds from BookedExternalMembers table for the user
            var bookedExternalMeetingIds = await Db.BookedMeetingMembers
                .Where(x => x.UserId == userId).Select(x => x.BookedMeetingId).ToListAsync();
            if (bookedExternalMeetingIds.Any())
            {
                bookedExternalMeetings.AddRange(await Db.BookedExternalMeeting.Where(x => bookedExternalMeetingIds.Contains(x.Id.ToString()) && !x.IsCancelled)
                   .Include(x => x.ExternalMeeting).Include(x => x.CustomQuestionAnswer).ToListAsync());

                bookedExternalMeetings = bookedExternalMeetings.Distinct().ToList();
            }

            foreach (var bookedExternalMeeting in bookedExternalMeetings)
            {
                var meetingMembers = Db.BookedMeetingMembers.Where(x => x.BookedMeetingId == bookedExternalMeeting.Id.ToString()).ToList();
                var meetingOwner = Db.UserProfiles
                    .Where(x => x.UserId == bookedExternalMeeting.ExternalMeeting.CreatedBy.ToString())
                    .Select(x => new UserMDVm
                    {
                        FirstName = x.FirstName,
                        LastName = x.LastName,
                        Email = x.Email,
                        ProfilePictureUrl = x.ProfilePictureUrl,
                        Id = x.UserId
                    }).FirstOrDefault();

                if (meetingOwner.ProfilePictureUrl is not null)
                    meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);
                bookedExternalMeeting.MeetingOwner = meetingOwner;

                var members = meetingMembers.Select(x =>
                {
                    var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                    if (user != null)
                    {
                        return new UserMDVm
                        {
                            Id = user.UserId,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email,
                            PhoneNumber = user.PhoneNumber,
                            ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                        };
                    }

                    return new UserMDVm();
                }).ToList();
                bookedExternalMeeting.InternalMembers = members.Where(m => m.Id != null || m.Email != null).ToList();

                bookedExternalMeeting.ExternalMembers = meetingMembers
                    .Where(x => x.Email != null && x.UserId == null)
                    .Select(x => x.Email).ToList();
            }

            return bookedExternalMeetings;
        }
        #endregion

        #region Get All Booked External Meeting
        public async Task<GenericResponse> GetAllBookedExternalMeetingOrEvent()
        {
            var bookedExternalMeetings = await Db.BookedExternalMeeting
                .Include(x => x.ExternalMeeting)
                .Include(x => x.CustomQuestionAnswer).ToListAsync();

            return new GenericResponse
            {
                ResponseMessage = "External Meetings retrived successfully",
                ResponseCode = "200",
                Data = bookedExternalMeetings
            };
        }
        #endregion

        #region Get All External Meeting/Event
        public async Task<GenericResponse> GetAllExternalMeetingOrEvent()
        {
            List<GetTeamMemberExternalMeetings> getTeamMemberExternalMeetings = new List<GetTeamMemberExternalMeetings>();
            var externalMeetings = await Db.ExternalMeeting
               .Include(x => x.ExternalMeetingQuestion)
               .Include(x => x.CustomQuestion).ToListAsync();
            if (!externalMeetings.Any()) throw new RecordNotFoundException("No External meeting found for the user");

            // Get the personal schedule of the external meeting
            var personalSchs = new List<PersonalSchedule>();
            foreach (var externalMeet in externalMeetings)
            {
                var externalMeetingTimeBreakDowns = await Db.ExternalMeetingTimeManagements
               .Where(x => x.ExternalMeetingId == externalMeet.Id).ToListAsync();
                externalMeet.ExternalMeetingTimeBreakDowns = externalMeetingTimeBreakDowns;

                var response = new GetTeamMemberExternalMeetings();
                response.ExternalMeeting = externalMeet;

                // Get the user that created the meeting details using externalMeeting CreatedBy
                var createdBy = await Db.UserProfiles.Where(x => x.UserId == externalMeet.CreatedBy.ToString())
                    .Select(u => new UserDto
                    {
                        Id = u.UserId.ToString(),
                        FirstName = u.FirstName,
                        LastName = u.LastName,
                        Email = u.Email,
                        PhoneNumber = u.PhoneNumber,
                        ProfileUrl = u.ProfilePictureUrl
                    }).FirstOrDefaultAsync();

                if (createdBy.ProfileUrl is not null)
                {
                    var profileSignedUrl = await _aWSS3Sevices.GetSignedUrlAsync(createdBy.ProfileUrl);
                    createdBy.ProfileUrl = profileSignedUrl;
                }

                response.CreatedBy = createdBy;
                getTeamMemberExternalMeetings.Add(response);
            }

            return new GenericResponse
            {
                ResponseMessage = "External Meetings retrived successfully",
                ResponseCode = "200",
                Data = getTeamMemberExternalMeetings.OrderByDescending(x => x.ExternalMeeting.CreatedAt)
            };
        }
        #endregion

        #region Lock external meeting
        /// <summary>
        /// Lock External Meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="lockMeeting"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> LockExternalMeetingOrEvent(string meetingId, bool lockMeeting, string loggedInUserId)
        {
            var externalMeeting = await Db.ExternalMeeting.Where(x => x.Id.ToString() == meetingId).FirstOrDefaultAsync();
            if (externalMeeting == null) throw new RecordNotFoundException("External meeting not found");

            // Check for permission
            await CheckPermission(externalMeeting.CreatedBy.ToString(), loggedInUserId);

            externalMeeting.IsLocked = lockMeeting;
            Db.ExternalMeeting.Update(externalMeeting);
            var res = await Db.SaveChangesAsync();
            return res > 0;
        }
        #endregion

        #region Mark meeting as happened
        /// <summary>
        /// Mark meeting as happened
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<GenericResponse> MarkMeetingAsHappened(string meetingId)
        {
            var meeting = await Db.CalenderMeetings
                //.Include(x => x.SubsequentMeetingDates)
                .FirstOrDefaultAsync(x => x.Id.ToString() == meetingId);
            if (meeting is null)
                throw new RecordNotFoundException("Meeting not found");

            //if (meeting.SubsequentMeetingDates.Any())
            //{
            //    foreach (var subMeeting in meeting.SubsequentMeetingDates.OrderBy(x => x.SubsequentMeetingDateTime))
            //    {
            //        // Do nothing for now
            //    }
            //}

            meeting.HasThisMeetingHappened = true;
            Db.CalenderMeetings.Update(meeting);
            var res = await Db.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? "Meeting marked as happened successfully" : "Failed",
                Data = res > 0
            };
        }
        #endregion

        #region Private Methods - Mostly Validations
        /// <summary>
        /// Get latest proposed meeting date
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        private async Task<ProposedDateDetail> GetProposedNewMeetingDateDeatils(string meetingId)
        {
            var proposedNewMeetngDate = await Db.ProposedDateDetails
                .Where(x => x.MeetingId.ToString() == meetingId && x.Status != ProposedNewDateStatus.Accepted && x.Status != ProposedNewDateStatus.Rejected)
                .OrderByDescending(x => x.PropsedOn)
                .FirstOrDefaultAsync();
            return proposedNewMeetngDate;
        }

        /// <summary>
        /// This chks if a user has permission to perform an action
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permission"></param>
        /// <returns></returns>
        private async Task<bool> CheckUserPermission(string userId, Permissions permission)
        {
            var userPermissions = await _adminService.GetUserPermissions(userId);

            userPermissions = userPermissions.Select(x => x.Replace("'", "")).ToList();
            var enumPermissions = userPermissions?.Select(x => Enum.Parse<Permissions>(x)).ToList();
            if (enumPermissions != null && enumPermissions.Any() && enumPermissions.Contains(permission) || enumPermissions != null && enumPermissions.Any() && enumPermissions.Contains(Permissions.all))
                return true;

            return false;
        }

        /// <summary>
        /// Custom frequency validations for creating and updating calender meeting
        /// </summary>
        /// <param name="customFrequencyDto"></param>
        /// <param name="startDate"></param>
        /// <exception cref="DirtyFormException"></exception>
        /// <exception cref="Exception"></exception>
        private static void CustomFrequencyValidations(CustomFrequencyDto customFrequencyDto, DateTime startDate)
        {
            // Custom frequency validations
            if (customFrequencyDto.RepeatCount == 0)
                throw new DirtyFormException("Repeat count cannot be zero, default value is 1");

            var repeatEvery = new List<string> { "day", "week", "month", "year" };
            if (!repeatEvery.Contains(customFrequencyDto.RepeatEvery.ToLower()))
                throw new Exception("'Repeat Every' field must be either day, week, month or year");
            if (customFrequencyDto.EndStatus == EndStatus.After && customFrequencyDto.EndsAfter == 0)
                throw new DirtyFormException("'Ends After' field cannot be zero");
            if (customFrequencyDto.EndStatus == EndStatus.On && customFrequencyDto.EndsOn == null)
                throw new DirtyFormException("'Ends On' field cannot be null, please select a value");
            if (customFrequencyDto.RepeatEvery.ToLower() == "week" && customFrequencyDto.RepeatOn == null)
                throw new DirtyFormException("'Repeat On' field cannot be null for weekly frequency, please select a value");

            //if (customFrequencyDto.RepeatEvery.ToLower() == "day")
            //    customFrequencyDto.RepeatOn = null;

            if (customFrequencyDto.RepeatEvery.ToLower() == "month" && customFrequencyDto.RepeatOn == null)
                throw new DirtyFormException("'Repeat On' cannot be null for monthly frequency, please select a valu4");
            if (customFrequencyDto.RepeatEvery.ToLower() == "year" && customFrequencyDto.RepeatOn == null)
                throw new DirtyFormException("'Repeat On' cannot be null for yearly frequency, please select a value");

            int.TryParse(customFrequencyDto.RepeatOn, out int repeatOn);
            if (customFrequencyDto.RepeatEvery.ToLower() == "month" && repeatOn > 31)
                throw new Exception("'Repeat On' cannot be greater than 31 for monthly frequency");
            if (customFrequencyDto.RepeatEvery.ToLower() == "year" && repeatOn > 12)
                throw new Exception("'Repeat On' cannot be greater than 12 for yearly frequency");
            if (customFrequencyDto.RepeatEvery.ToLower() == "week" && repeatOn > 7)
                throw new Exception("'Repeat On' cannot be greater than 7 for weekly frequency");
            if (customFrequencyDto.EndStatus == EndStatus.After && customFrequencyDto.EndsAfter < 1)
                throw new DirtyFormException("'Ends After' cannot be less than 1");
            if (customFrequencyDto.EndStatus == EndStatus.On && customFrequencyDto.EndsOn < GetAdjustedDateTimeBasedOnTZNow())
                throw new DirtyFormException("'Ends On' date cannot be less than the current date");
            if (customFrequencyDto.EndStatus == EndStatus.On && customFrequencyDto.EndsOn < startDate)
                throw new DirtyFormException("'Ends On' date cannot be less than the meeting start date");
            if (customFrequencyDto.EndStatus == EndStatus.Never)
            {
                customFrequencyDto.EndsAfter = 20;
                customFrequencyDto.EndsOn = null;
            }
        }

        /// <summary>
        /// Pay load validations for creating and updating external meetings
        /// </summary>
        /// <param name="externalMeetingDto"></param>
        /// <param name="personalSchs"></param>
        /// <param name="externalMeetingId"></param>
        /// <returns></returns>
        /// <exception cref="DirtyFormException"></exception>
        /// <exception cref="RecordAlreadyExistException"></exception>
        /// <exception cref="RecordNotFoundException"></exception>
        private async Task PayloadValidationsForExternalMeetings(ExternalMeetingDto externalMeetingDto, List<PersonalSchedule> personalSchs, Guid? externalMeetingId = null)
        {
            // Validate that PersonalScheduleDtos is provided when PersonalSchedule is Custom
            if (externalMeetingDto.PersonalSchedule == PersonalScheduleType.Custom && !externalMeetingDto.PersonalScheduleDtos.Any())
                throw new DirtyFormException($"PersonalScheduleDtos is required when ScheduleName is Custom");

            // Validate that all PersonalScheduleDtos have ExternalMeetingId set when PersonalSchedule is Custom
            if (externalMeetingDto.PersonalSchedule == PersonalScheduleType.Custom && externalMeetingDto.PersonalScheduleDtos.Any())
            {
                foreach (var scheduleDto in externalMeetingDto.PersonalScheduleDtos)
                {
                    if (scheduleDto.MeetingId == null)
                        throw new DirtyFormException($"ExternalMeetingId is required for all PersonalScheduleDtos when ScheduleName is Custom");
                }
            }

            if (personalSchs.Count == 0) { throw new DirtyFormException($"You do not have personal schedules for {externalMeetingDto.PersonalSchedule.ToString().ToTitleCase()} schedule type"); }

            if (externalMeetingDto.CanBookEndDate < GetAdjustedDateTimeBasedOnTZNow() || externalMeetingDto.CanBookStartDate < GetAdjustedDateTimeBasedOnTZNow())
                throw new DirtyFormException("Can book start or end date cannot be in the past");
            if (externalMeetingDto.CanBookEndDate < externalMeetingDto.CanBookStartDate)
                throw new DirtyFormException("Can book end date cannot be less the that can book start date");
            if (externalMeetingDto.ExternalMeetingType == ExternalMeetingType.RoundRobin && string.IsNullOrEmpty(externalMeetingDto.MeetingOwnerId))
                throw new DirtyFormException("Meeting Host is required for a Round Robin Meeting");

            // Check if external meeting with the same title already exist
            var externalMeetingExist = await Db.ExternalMeeting.Where(x => x.MeetingName.ToLower() == externalMeetingDto.MeetingName.ToLower() && x.CanBookEndDate >= GetAdjustedDateTimeBasedOnTZNow()).FirstOrDefaultAsync();
            //if (externalMeetingExist != null && externalMeetingId != null && externalMeetingExist.Id != externalMeetingId.Value) { throw new RecordAlreadyExistException($"External meeting with '{externalMeetingDto.MeetingName}' name already exist"); }
            //if (externalMeetingExist != null && externalMeetingId == null) { throw new RecordAlreadyExistException($"External meeting with '{externalMeetingDto.MeetingName}' name already exist"); }

            // If the 'canbookenddate' is the current date(the day's date), make sure that the time chosen has not passed
            // and that the creator has an avaiable schedule time for the remaining time of the day
            if (externalMeetingDto.CanBookEndDate.Date == GetAdjustedDateTimeBasedOnTZNow().Date)
            {
                var creatorPersonalScheduleforTheSelectedDate = personalSchs
                    .Where(p => p.Day == externalMeetingDto.CanBookEndDate.DayOfWeek.ToString()).FirstOrDefault();
                if (creatorPersonalScheduleforTheSelectedDate.StartTime.TimeOfDay <= externalMeetingDto.CanBookStartDate.TimeOfDay && creatorPersonalScheduleforTheSelectedDate.EndTime.TimeOfDay >= externalMeetingDto.CanBookEndDate.TimeOfDay)
                {
                    // Check if the creator's availability can accomodate the proposed duration for the meeting
                    var availabilityTimeDiffForTheDay = creatorPersonalScheduleforTheSelectedDate.EndTime - creatorPersonalScheduleforTheSelectedDate.StartTime;
                    if (availabilityTimeDiffForTheDay.Minutes <= externalMeetingDto.MeetingDuration)
                        throw new DirtyFormException($"The proposed meeting duration is higher than your availabile minutes for the day. Proposed: {externalMeetingDto.MeetingDuration} minutes - Actual Availability: {availabilityTimeDiffForTheDay.Minutes} minutes");
                }
                else if (creatorPersonalScheduleforTheSelectedDate.StartTime.TimeOfDay <= externalMeetingDto.CanBookStartDate.TimeOfDay && creatorPersonalScheduleforTheSelectedDate.EndTime.TimeOfDay < externalMeetingDto.CanBookEndDate.TimeOfDay)
                {
                    throw new DirtyFormException("The 'End Time' for bookings is outside your availability for the day. Either adjust your availability for the day or reduce the window for which people can book your meeting");
                }
                else if (creatorPersonalScheduleforTheSelectedDate.StartTime.TimeOfDay > externalMeetingDto.CanBookStartDate.TimeOfDay && creatorPersonalScheduleforTheSelectedDate.EndTime.TimeOfDay >= externalMeetingDto.CanBookEndDate.TimeOfDay)
                {
                    throw new DirtyFormException("The 'Start Time' for bookings is not within your availability for the day. Either adjust your availability for the day or reduce the window for which people can book your meeting");
                }
                else
                {
                    throw new DirtyFormException("Both the 'End Time' and 'Start Time' for bookings is not within your availability for the day. Either adjust your availability for the day or reduce the window for which people can book your meeting");
                }
            }
        }

        /// <summary>
        /// Process booking for group meeting
        /// </summary>
        /// <param name="model"></param>
        /// <param name="externalMeeting"></param>
        /// <param name="bookedExternalMeet"></param>
        /// <param name="scheduleName"></param>
        /// <param name="oldSelectedTime"></param>
        /// <exception cref="OperationNotAllowedException"></exception>
        private async Task ProcessBookingForGroupMeeting(BookExternalMeetingDto model, ExternalMeeting externalMeeting, BookedExternalMeeting bookedExternalMeet, PersonalScheduleType scheduleName, string oldSelectedTime = null)
        {
            var selectedMeetingTimeMgt = await Db.ExternalMeetingTimeManagements
                               .Where(x => x.UserId == externalMeeting.CreatedBy.ToString() && x.ScheduleName == scheduleName.ToString() && x.DayOfTheWeek == model.SelectedDateAndTime.DayOfWeek.ToString() && x.ExternalMeetingId == externalMeeting.Id)
                               .FirstOrDefaultAsync();

            var bookedMeetingCountForSelectedTimeAndDate = await Db.BookedExternalMeeting
                .Where(meet => meet.SelectedDateAndTime == model.SelectedDateAndTime && !meet.IsCancelled && meet.ExternalMeetingId == externalMeeting.Id)
                .CountAsync();

            if (selectedMeetingTimeMgt == null)
                throw new OperationNotAllowedException("Meeting Owner not available on this day");

            var selectedTime = model.SelectedDateAndTime.ToShortTimeString();
            var selectedTime12hrs = DateTime.Parse(selectedTime).ToString("hh:mm tt");
            var selectedDay = model.SelectedDateAndTime.DayOfWeek.ToString();
            var personalSchTimeBreakDown = new List<string>();

            var selectedTimeSlots = new List<string>();
            if (!string.IsNullOrEmpty(selectedMeetingTimeMgt.SelectedTimeSlots))
                selectedTimeSlots = selectedMeetingTimeMgt.SelectedTimeSlots.Split(",").ToList();

            if (!string.IsNullOrEmpty(selectedMeetingTimeMgt.TimeBreakDown))
                personalSchTimeBreakDown = selectedMeetingTimeMgt.TimeBreakDown.Split(",").ToList();

            foreach (var time in personalSchTimeBreakDown)
            {
                // convert from 24hrs time format to 12hrs time format
                var time12hrs = DateTime.Parse(time).ToString("hh:mm tt");
                if (selectedTime12hrs == time12hrs)
                {
                    if (externalMeeting.AvoidConflicts)
                    {
                        personalSchTimeBreakDown.Remove(time);
                        selectedTimeSlots.Add(time);
                    }
                    else
                    {
                        // Check the count of booked meeting for that time
                        if (bookedMeetingCountForSelectedTimeAndDate == externalMeeting.MaxInvitePerMeeting)
                        {
                            personalSchTimeBreakDown.Remove(time);
                            selectedTimeSlots.Add(time);
                        }
                    }

                    bookedExternalMeet.PersonalScheduleId = selectedMeetingTimeMgt.PersonalScheduleId.ToString();
                    break;
                }
            }

            // Check if the selected time has been added to 'SelectedTimeSlots'
            if (oldSelectedTime is not null)
            {
                if (selectedMeetingTimeMgt.SelectedTimeSlots.Contains(oldSelectedTime))
                {
                    personalSchTimeBreakDown.Add(oldSelectedTime);
                    selectedTimeSlots.Remove(oldSelectedTime);
                }
            }

            if (bookedMeetingCountForSelectedTimeAndDate == externalMeeting.MaxInvitePerMeeting)
            {
                if (personalSchTimeBreakDown.Any())
                    selectedMeetingTimeMgt.TimeBreakDown = string.Join(",", personalSchTimeBreakDown);
                else
                    selectedMeetingTimeMgt.TimeBreakDown = null;

                if (selectedTimeSlots.Any())
                    selectedMeetingTimeMgt.SelectedTimeSlots = string.Join(",", selectedTimeSlots);
                else
                    selectedMeetingTimeMgt.SelectedTimeSlots = null;
            }

            if (bookedExternalMeet.PersonalScheduleId is null)
                throw new OperationNotAllowedException("The selected time is not available for this meeting or event");

            // Unlock the selected time slot
            selectedMeetingTimeMgt.IsLockedTill = null;
            Db.ExternalMeetingTimeManagements.UpdateRange(selectedMeetingTimeMgt);
        }

        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        private async Task CheckPermission(string createdBy, string loggedInUserId)
        {
            // Check if the logged in user is a super admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (createdBy != loggedInUserId && userRole != DataSeeder.SuperAdmin)
                throw new UnauthorizedAccessException("You are either not the onwner of the meeting or" +
                    " you do not have the permission to perform this action");
        }        
        
        /// <summary>
        /// Process booking for one on one meeting
        /// </summary>
        /// <param name="selectedTimeSlot"></param>
        /// <param name="createdBy"></param>
        /// <param name="bookedExternalMeet"></param>
        /// <param name="scheduleName"></param>
        /// <param name="externalMeeting"></param>
        /// <exception cref="OperationNotAllowedException"></exception>
        private void ProcessBookingForOneOnOneOrRoundRobinMeeting(DateTime selectedTimeSlot, Guid createdBy, BookedExternalMeeting bookedExternalMeet, PersonalScheduleType scheduleName, ExternalMeeting externalMeeting)
        {
            var selectedMeetingTimeMgt = Db.ExternalMeetingTimeManagements
                                    .Where(x => x.UserId == createdBy.ToString() && x.ScheduleName == scheduleName.ToString() &&
                                    x.DayOfTheWeek == selectedTimeSlot.DayOfWeek.ToString() && x.ExternalMeetingId == bookedExternalMeet.ExternalMeetingId).FirstOrDefault();

            var selectedTime = selectedTimeSlot.ToShortTimeString();
            var selectedTime12hrs = DateTime.Parse(selectedTime).ToString("hh:mm tt");
            var selectedDay = selectedTimeSlot.DayOfWeek.ToString();
            var selectedTimeSlots = new List<string>();
            var personalSchTimeBreakDown = new List<string>();

            if (selectedMeetingTimeMgt == null)
                throw new OperationNotAllowedException("Meeting Owner not available on this day: " + selectedTimeSlot.DayOfWeek.ToString());

            if (selectedMeetingTimeMgt.SelectedTimeSlots != null && selectedMeetingTimeMgt.SelectedTimeSlots.Split(",").Contains(selectedTime))
                throw new OperationNotAllowedException("The selected time is no longer available for this meeting or event");

            if (!string.IsNullOrEmpty(selectedMeetingTimeMgt.SelectedTimeSlots))
                selectedTimeSlots = selectedMeetingTimeMgt.SelectedTimeSlots.Split(",").ToList();

            personalSchTimeBreakDown = selectedMeetingTimeMgt.TimeBreakDown.Split(",").ToList();

            // Get the booked meeting count outside the loop to avoid DbContext concurrency issues
            var bookedMeetingCountForSelectedTimeAndDate = Db.BookedExternalMeeting
                .Where(meet => meet.SelectedDateAndTime == selectedTimeSlot && !meet.IsCancelled && meet.ExternalMeetingId == bookedExternalMeet.ExternalMeetingId)
                .Count();

            foreach (var time in personalSchTimeBreakDown)
            {
                // convert from 24hrs time format to 12hrs time format
                var time12hrs = DateTime.Parse(time).ToString("hh:mm tt");
                if (selectedTime12hrs == time12hrs)
                {
                    if (externalMeeting.AvoidConflicts)
                    {
                        personalSchTimeBreakDown?.Remove(time);
                        selectedTimeSlots.Add(time);
                    }
                    else
                    {
                        // Use the pre-calculated count to avoid DbContext concurrency issues
                        if (bookedMeetingCountForSelectedTimeAndDate >= externalMeeting.MaxInvitePerMeeting)
                        {
                            personalSchTimeBreakDown?.Remove(time);
                            selectedTimeSlots.Add(time);
                        }
                    }
                    bookedExternalMeet.PersonalScheduleId = selectedMeetingTimeMgt.PersonalScheduleId.ToString();
                    break;
                }
            }
            if (personalSchTimeBreakDown.Any())
                selectedMeetingTimeMgt.TimeBreakDown = string.Join(",", personalSchTimeBreakDown.Distinct());
            else
                selectedMeetingTimeMgt.TimeBreakDown = null;

            if (selectedTimeSlots.Any())
                selectedMeetingTimeMgt.SelectedTimeSlots = string.Join(",", selectedTimeSlots.Distinct());
            else
                selectedMeetingTimeMgt.SelectedTimeSlots = null;

            if (bookedExternalMeet.PersonalScheduleId is null)
                throw new OperationNotAllowedException("The selected time is not available for this meeting or event");

            // Unlock the selected time slot
            selectedMeetingTimeMgt.IsLockedTill = null;
            Db.ExternalMeetingTimeManagements.UpdateRange(selectedMeetingTimeMgt);
        }

        /// <summary>
        /// Log the job id
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="jobId"></param>
        /// <returns></returns>
        private async Task LogJobId(string eventId, string jobId, JobType jobType = JobType.Scheduled)
        {
            await Db.BackGroundJobIds.AddAsync(new BackGroundJobId
            {
                JobId = jobId,
                EventId = eventId,
                JobType = jobType
            });
            await Db.SaveChangesAsync();
        }

        /// <summary>
        /// Get background jobId
        /// </summary>
        /// <param name="eventId"></param>
        /// <returns></returns>
        private async Task<string> GetJobId(string eventId)
        {
            return await Db.BackGroundJobIds.Where(x => x.EventId == eventId)
                .Select(x => x.JobId).FirstOrDefaultAsync();
        }

        /// <summary>
        /// Remove background jobId
        /// </summary>
        /// <param name="jobId"></param>
        /// <returns></returns>
        private async Task RemoveJobId(string jobId)
        {
            var job = await Db.BackGroundJobIds.Where(x => x.JobId == jobId).FirstOrDefaultAsync();
            if (job is not null)
            {
                Db.BackGroundJobIds.Remove(job);
                await Db.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Choose meeting host based on availability
        /// </summary>
        /// <param name="model"></param>
        /// <param name="externalMeeting"></param>
        /// <param name="scheduleName"></param>
        /// <returns></returns>
        private async Task ChooseMeetingHost(BookExternalMeetingDto model, ExternalMeeting externalMeeting, PersonalScheduleType scheduleName)
        {
            // Get the invitee name
            var inviteeDetails = Db.UserProfiles.Where(x => x.UserId == externalMeeting.MeetingOwnerId)
                .Select(x => new { f = x.FirstName, l = x.LastName, email = x.Email })
                .FirstOrDefault();
            var invitee = inviteeDetails.f + " " + inviteeDetails.l;
            var inviteeEmail = inviteeDetails.email;

            var hostSchedules = new List<PersonalSchedule>();
            var currentHost = "";
            var isAvailable = false;
            var hostingOrders = await Db.RoundRobinHostingOrders.Where(x => x.ExternalMeetingId == externalMeeting.Id)
                    .OrderBy(x => x.HostCount)
                    .ToListAsync();
            if (!hostingOrders.Any())
            {
                // Check if the meeting owner is available
                hostSchedules = await Db.PersonalSchedule
                    .Where(x => x.UserId.ToString() == externalMeeting.MeetingOwnerId && x.Day == model.SelectedDateAndTime.DayOfWeek.ToString() && x.ScheduleName == scheduleName && x.Available).ToListAsync();

                foreach (var sch in hostSchedules)
                {
                    // Check if for the selected time, the host is available
                    if (sch.StartTime.TimeOfDay <= model.SelectedDateAndTime.TimeOfDay && sch.EndTime.TimeOfDay >= model.SelectedDateAndTime.TimeOfDay)
                    {
                        isAvailable = true;
                        break;
                    }
                }

                if (isAvailable)
                {
                    await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                    {
                        ExternalMeetingId = externalMeeting.Id,
                        HostCount = 1,
                        HostId = externalMeeting.MeetingOwnerId,
                        MeetingDateAndTime = model.SelectedDateAndTime
                    });
                    currentHost = externalMeeting.MeetingOwnerId;

                    var otherHosts = externalMeeting.OtherMeetingHostsIds.Split(",").ToList();
                    foreach (var host in otherHosts)
                    {
                        await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            HostCount = 0,
                            HostId = host.Trim(),
                            MeetingDateAndTime = model.SelectedDateAndTime
                        });
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(externalMeeting.OtherMeetingHostsIds))
                    {
                        var otherHosts = externalMeeting.OtherMeetingHostsIds.Split(",").ToList();
                        otherHosts.Add(externalMeeting.MeetingOwnerId);
                        foreach (var host in otherHosts)
                        {
                            hostSchedules = await Db.PersonalSchedule
                                .Where(x => x.UserId.ToString() == host.Trim() && x.Day == model.SelectedDateAndTime.DayOfWeek.ToString() && x.ScheduleName == scheduleName && x.Available).ToListAsync();

                            foreach (var sch in hostSchedules)
                            {
                                // Check if for the selected time, the host is available
                                if (sch.StartTime.TimeOfDay >= model.SelectedDateAndTime.TimeOfDay && sch.EndTime.TimeOfDay <= model.SelectedDateAndTime.TimeOfDay)
                                {
                                    isAvailable = true;
                                    break;
                                }
                            }

                            if (isAvailable)
                            {
                                await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                                {
                                    ExternalMeetingId = externalMeeting.Id,
                                    HostCount = 1,
                                    HostId = host.Trim(),
                                    MeetingDateAndTime = model.SelectedDateAndTime
                                });
                                currentHost = host.Trim();
                                break;
                            }
                            else
                            {
                                await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                                {
                                    ExternalMeetingId = externalMeeting.Id,
                                    HostCount = 0,
                                    HostId = host.Trim(),
                                    MeetingDateAndTime = model.SelectedDateAndTime
                                });
                            }
                        }
                    }
                }
            }
            else
            {
                foreach (var hostingOrder in hostingOrders)
                {
                    // Check for the available host from the potential hosts
                    hostSchedules = await Db.PersonalSchedule
                        .Where(x => x.UserId.ToString() == hostingOrder.HostId && x.Day == model.SelectedDateAndTime.DayOfWeek.ToString() && x.ScheduleName == scheduleName && x.Available).ToListAsync();

                    foreach (var sch in hostSchedules)
                    {
                        // Check if for the selected time, the host is available
                        if (sch.StartTime.TimeOfDay <= model.SelectedDateAndTime.TimeOfDay && sch.EndTime.TimeOfDay >= model.SelectedDateAndTime.TimeOfDay)
                        {
                            isAvailable = true;
                            break;
                        }
                    }

                    if (isAvailable)
                    {
                        hostingOrder.HostCount++;
                        Db.RoundRobinHostingOrders.Update(hostingOrder);
                        currentHost = hostingOrder.HostId;
                        break;
                    }
                }
            }

            if (currentHost == "")
            {
                foreach (var hostingOrder in hostingOrders)
                {
                    if (hostingOrder.HostId == externalMeeting.MeetingOwnerId)
                    {
                        await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            HostCount = hostingOrder.HostCount++,
                            HostId = externalMeeting.MeetingOwnerId,
                            MeetingDateAndTime = model.SelectedDateAndTime
                        });
                        currentHost = externalMeeting.MeetingOwnerId;
                    }
                }

                if (currentHost == "")
                {
                    await Db.RoundRobinHostingOrders.AddAsync(new RoundRobinHostingOrder
                    {
                        ExternalMeetingId = externalMeeting.Id,
                        HostCount = 1,
                        HostId = externalMeeting.MeetingOwnerId,
                        MeetingDateAndTime = model.SelectedDateAndTime
                    });
                    currentHost = externalMeeting.MeetingOwnerId;
                }
            }

            // Send mail to the current host
            //if (currentHost == "")
            //    throw new OperationNotAllowedException("None of the meeting hosts is available at the selected time, kindly selected a different date or time.");

            var existingOtherMeetingHosts = externalMeeting.OtherMeetingHostsIds;
            var otherMeetingHostsIds = new List<string>();
            if (!string.IsNullOrEmpty(existingOtherMeetingHosts))
                otherMeetingHostsIds = existingOtherMeetingHosts.Split(",").Select(x => x.Trim()).ToList();

            var message = $"You have been scheduled as the next host to a Round Robin meeting you were added to by {invitee}";
            var otherMeetingHostsName = new List<string>();
            var otherMeetingHosts = await Db.UserProfiles.Where(x => externalMeeting.OtherMeetingHostsIds.Contains(x.UserId))
                .ToListAsync();
            var currentHostUserVm = await Db.UserProfiles.Where(x => x.UserId == currentHost).ToListAsync();

            foreach (var host in otherMeetingHosts)
                otherMeetingHostsName.Add(host.FirstName + " " + host.LastName);

            var invitedUsers = new List<UserProfile>();
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/meeting_template.html");
            var templateFromFolder = File.ReadAllText(templatePath);

            var calenderMeeting = new CalenderMeeting
            {
                Id = externalMeeting.Id,
                Location = externalMeeting.Location,
                MeetingDuration = externalMeeting.MeetingDuration.ToString(),
                StartDate = externalMeeting.MeetingStartDateRange.Value,
                MeetingLink = externalMeeting.MeetingLink,
                Name = externalMeeting.MeetingName
            };
            SendMailToInternalMembers(model.SubDomain, invitee, inviteeEmail, calenderMeeting, templateFromFolder, otherMeetingHostsName, currentHostUserVm, false, "Your The Next Host", message, Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
        }

        /// <summary>
        /// Send External Meeting Invitation
        /// </summary>
        /// <param name="externalMeeting"></param>
        /// <param name="loggedInUser"></param>
        /// <param name="bookedExternalMeet"></param>
        private void SendExternalMeetingInvitation(ExternalMeeting externalMeeting, UserProfile loggedInUser, BookedExternalMeeting bookedExternalMeet)
        {
            var html = $"You have been invited to book a {externalMeeting.ExternalMeetingType} meeting - {externalMeeting.MeetingName} by {loggedInUser.FirstName} {loggedInUser.LastName}. <br/> <br/> Please click on the link below to book a spot. <br/> <br/> {externalMeeting.BookingLink}";

            var emails = bookedExternalMeet.GuestEmails.Split(",").ToList();
            BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(html, emails, $"External {externalMeeting.ExternalMeetingType} Meeting Invitation"));
        }

        /// <summary>
        /// Send External Meeting Reminder for reoccurring meetings
        /// </summary>
        /// <param name="calenderVm"></param>
        /// <param name="meeting"></param>
        /// <param name="template"></param>
        /// <param name="email"></param>
        private async void SendNotificationsForReoccuringMeetings(CalenderVm calenderVm, CalenderMeeting meeting, string template, string email)
        {
            var jobId = "";
            if (string.IsNullOrEmpty(calenderVm.Frequency) && calenderVm.CustomFrequency is not null)
            {
                // Dictionnary of days in a week
                var days = new Dictionary<string, int>
                {
                    { "Sunday", 0 },
                    { "Monday", 1 },
                    { "Tuesday", 2 },
                    { "Wednesday", 3 },
                    { "Thursday", 4 },
                    { "Friday", 5 },
                    { "Saturday", 6 }
                };
                var repeatsOn = calenderVm.CustomFrequency.RepeatOn?.Split(',');
                var minute = GetAdjustedDateTimeBasedOnTZNow().AddMinutes(-20).Minute;
                var hour = GetAdjustedDateTimeBasedOnTZNow().AddMinutes(-20).Hour;
                var day = calenderVm.CustomFrequency.RepeatOn == null ? GetAdjustedDateTimeBasedOnTZNow().Day : days[repeatsOn[0]];
                switch (calenderVm.CustomFrequency.RepeatEvery)
                {
                    case "Day":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                        x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                        calenderVm.SubDomain), $"{minute} {hour} */{calenderVm.CustomFrequency.RepeatCount} * *");
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    case "Week":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());

                        foreach (var d in repeatsOn)
                        {
                            var diff = 0;
                            var weekDate = new DateTime();
                            if ((int)meeting.StartDate.DayOfWeek == days[d])
                            {
                                diff = 7;
                                weekDate = meeting.StartDate.AddDays(diff);
                            }
                            else if ((int)meeting.StartDate.DayOfWeek < days[d])
                                diff = 7 - days[d] - (int)meeting.StartDate.DayOfWeek;
                            else if ((int)meeting.StartDate.DayOfWeek > days[d])
                                diff = 7 - (int)meeting.StartDate.DayOfWeek - days[d];

                            RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                            x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                            calenderVm.SubDomain), $"{minute} {hour} */{calenderVm.CustomFrequency.RepeatCount * diff} * *");
                            jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        }

                        break;
                    case "Month":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                        x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                        calenderVm.SubDomain), $"{minute} {hour} {day} */{calenderVm.CustomFrequency.RepeatCount} *");
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    case "Year":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                       x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                       calenderVm.SubDomain), $"{minute} {hour} {day} */{calenderVm.CustomFrequency.RepeatCount * 12} *");
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (calenderVm.Frequency)
                {
                    case "Daily":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                        x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                        calenderVm.SubDomain), Cron.Daily);
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    case "Weekly":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                        x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                        calenderVm.SubDomain), Cron.Weekly);
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    case "Monthly":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                        x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                        calenderVm.SubDomain), Cron.Monthly);
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    case "Yearly":
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                        RecurringJob.AddOrUpdate<BackGroundService>(meeting.Id.ToString(), x =>
                       x.SendNotificationsForReoccurringMeetings(template, email, meeting.Id.ToString(),
                       calenderVm.SubDomain), Cron.Yearly);
                        jobId = RecurringJob.TriggerJob(meeting.Id.ToString());
                        break;
                    default:
                        break;
                }
            }

            // Add the jobId to the database
            await LogJobId(meeting.Id.ToString(), jobId, JobType.Recurring);
        }

        /// <summary>
        /// Deletes records from redis cache
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<bool> DeleteDataFromRedis(List<string> ids)
        {
            var result = true;
            var todoCacheKeys = new List<string>();
            if (GlobalVariables.CacheKeys.ContainsKey(_redisKey))
                todoCacheKeys = GlobalVariables.CacheKeys[_redisKey];

            foreach (var key in todoCacheKeys)
            {
                if (key.Split('_').ToList().Any(k => ids.Contains(k)))
                    result = await _redisCacheService.RemoveDataAsync(key);
                //else
                //    result = await _redisCacheService.RemoveDataAsync(key);
            }

            return result;
        }
        #endregion

        #region Private Methods - Send Mail To Both External And Internal Members - Change to Reddis
        public void ScheduleReminderForOneOffMeeting(CalenderVm calenderVm, string meetingId, string template, string email)
        {
            // Check if the meeting has a schedule background job for 'SendNotificationsForReoccurringMeetings'
            if (_cache.TryGetValue(meetingId, out string jobId))
            {
                BackgroundJob.Delete(jobId);
                _cache.Remove(meetingId);
            }

            var id = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, "Reminder: Meeting Invitation"), calenderVm.StartDate.AddMinutes(-calenderVm.NotifyMeInMinutes));
            _cache.Set(meetingId, id);
        }

        //Method Overloading the method to show meeting title
        public void ScheduleReminderForOneOffMeeting(CalenderVm calenderVm, string meetingId, string template, string email, string meetingTitle)
        {
            // Check if the meeting has a schedule background job for 'SendNotificationsForReoccurringMeetings'
            if (_cache.TryGetValue(meetingId, out string jobId))
            {
                BackgroundJob.Delete(jobId);
                _cache.Remove(meetingId);
            }
            var title = $"Reminder: Meeting Invitation - {meetingTitle}";
            var id = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, title), calenderVm.StartDate.AddMinutes(-calenderVm.NotifyMeInMinutes));
            _cache.Set(meetingId, id);
        }

        private void SendMailToExternalMembers(string subdomain, string invitee, string inviteeEmail, CalenderMeeting calender, string templateFromFolder, List<string> invitedMmemberNames, bool isAnUpdate, List<string> externalTeamMemberEmails, string subject, string message, string aiName, string aiImage, string organizer = null, List<string> attachment = null, bool sendSkillMail = false, MemoryStream icalenderStream = null)
        {
            var templateForSubsequentMeetings = "";
            foreach (var email in externalTeamMemberEmails)
            {
                var template = templateFromFolder;
                invitedMmemberNames.Remove(email);
                var inviteUrlYes = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/replies?MeetingId={1}&AcceptedUser={2}&ResponseType=yes&Invitee={3}&InviteeEmail={4}&AcceptedUserEmail={5}", subdomain, calender.Id, email, invitee, inviteeEmail, email);
                var inviteUrlNo = inviteUrlYes.Replace("yes", "no");
                var inviteUrlMaybe = inviteUrlYes.Replace("yes", "maybe");
                var proposeNewDateAndTimeUrl = inviteUrlYes.Replace("yes", "propose");

                var generateAgenda = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/suite/pkg/calendar/meeting-agenda/{1}", subdomain, calender.MeetingId);

                template = template.Replace("{name}", "Dear").Replace("{message}", message).Replace("{meeting name}", calender.Name).Replace("{location}", calender.Location)
                    .Replace("{yesurl}", inviteUrlYes).Replace("{nourl}", inviteUrlNo).Replace("{maybeurl}", inviteUrlMaybe).Replace("{duration}", calender.MeetingDuration)
                    .Replace("{link}", calender.MeetingLink).Replace("{organizer}", organizer ?? invitee).Replace("{ppnturl}", proposeNewDateAndTimeUrl).Replace("{invitee}", invitee)
                    .Replace("{more}", "https://joble.app/").Replace("{aiName}", aiName).Replace("{aiImage}", aiImage).Replace("{viewurl}", generateAgenda).Replace("{meetingId}", calender.MeetingId);

                templateForSubsequentMeetings = template;
                template = template.Replace("{date}", calender.StartDate.ToShortDateString()).Replace("{time}", calender.StartDate.ToShortTimeString());

                for (var i = 1; i <= invitedMmemberNames.Count; i++)
                {
                    var tem = "{" + i + "}";
                    template = template.Replace(tem, invitedMmemberNames[i - 1]);
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, invitedMmemberNames[i - 1]);
                }

                for (var j = invitedMmemberNames.Count + 1; j <= 20; j++)
                {
                    var tem = "{" + j + "}";
                    template = template.Replace(tem, "");
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, "");
                }

                for (var i = 1; i <= attachment?.Count; i++)
                {
                    var tem = "{attachmentURL" + i + "}";
                    template = template.Replace(tem, attachment[i - 1]);
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, attachment[i - 1]);
                }

                for (var j = attachment?.Count + 1; j <= 5; j++)
                {
                    var tem = "{attachmentVisibility" + j + "}";
                    template = template.Replace(tem, "display: none;");
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, "display: none;");
                }

                //subject = isAnUpdate ? $"Update - {subject}" : subject;
                if (icalenderStream != null)
                {
                    var icalender = new AttachmentDto
                    {
                        attachment = icalenderStream,
                        fileName = $"{calender.Name}.ics",
                    };

                    _emailService.SendEmailWithAttachments(template, email, subject, new List<AttachmentDto> { icalender });
                    //BackgroundJob.Enqueue(() => _emailService.SendEmailWithAttachments(template, email, subject, new List<AttachmentDto> { icalender }));
                }
                else
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, subject));

                // Trigger background job that will send out meeting reminders to members
                var calenderVm = new CalenderVm
                {
                    Frequency = calender.Frequency,
                    SubDomain = subdomain,
                    StartDate = calender.StartDate,
                    NotifyMeInMinutes = calender.NotifyMembersIn
                };

                if (calenderVm.CustomFrequency != null)
                {
                    calenderVm.CustomFrequency = new CustomFrequencyDto
                    {
                        RepeatCount = calender.CustomFrequency.RepeatCount,
                        RepeatEvery = calender.CustomFrequency.RepeatEvery,
                        RepeatOn = calender.CustomFrequency.RepeatOn,
                        EndsAfter = calender.CustomFrequency.EndsAfter,
                        EndsOn = calender.CustomFrequency.EndsOn,
                        EndStatus = calender.CustomFrequency.EndStatus
                    };
                }
                if (!calender.IsCancelled)
                {
                    if (calender.Frequency == "OneOff")
                        ScheduleReminderForOneOffMeeting(calenderVm, calender.Id.ToString(), template, email, calender.Name);
                    else
                    {
                        SendNotificationsForReoccuringMeetings(calenderVm, calender, template, email);
                    }
                }

                // Send skill set email notification
                if (sendSkillMail)
                {
                    var skillTemplate = ReadTemplateFromFile("meeting_skills", _environment);
                    var skillSubject = $"Confirm Your Skills: {calender.Name}";
                    var link = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/confirm-skills?meetingId={1}&email={2}", subdomain, calender.Id.ToString(), email);

                    skillTemplate = skillTemplate.Replace("{name}", "Dear").Replace("{meeting_name}", calender.Name)
                        .Replace("{invitee}", invitee).Replace("{aiName}", aiName).Replace("{link}", link).Replace("{aiImage}", aiImage); ;

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(skillTemplate, email, skillSubject));
                }
            }
        }

        private void SendMailToInternalMembers(string subdomain, string invitee, string inviteeEmail, CalenderMeeting calender, string templateFromFolder, List<string> invitedMmemberNames, List<UserProfile> invitedUsers, bool isAnUpdate, string subject, string message, string aiName, string aiImage, List<string> attachment = null, bool sendSkillMail = false, MemoryStream icalenderStream = null)
        {
            var templateForSubsequentMeetings = "";
            foreach (var user in invitedUsers)
            {
                invitedMmemberNames.Remove(user.FirstName + " " + user.LastName);
                var template = templateFromFolder;
                var inviteUrlYes = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/replies?MeetingId={1}&AcceptedUser={2}&ResponseType=yes&Invitee={3}&InviteeEmail={4}&AcceptedUserEmail={5}", subdomain, calender.Id, user.FirstName, invitee, inviteeEmail, user.Email);

                var inviteUrlNo = inviteUrlYes.Replace("yes", "no");
                var inviteUrlMaybe = inviteUrlYes.Replace("yes", "maybe");
                var proposeNewDateAndTimeUrl = inviteUrlYes.Replace("yes", "propose");
                var generateAgenda = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/suite/pkg/calendar/meeting-agenda/{1}", subdomain, calender.MeetingId);

                template = template.Replace("{name}", user.FirstName).Replace("{message}", message).Replace("{meeting name}", calender.Name).Replace("{location}", calender.Location)
                                    .Replace("{yesurl}", inviteUrlYes).Replace("{nourl}", inviteUrlNo).Replace("{maybeurl}", inviteUrlMaybe).Replace("{duration}", calender.MeetingDuration)
                                    .Replace("{link}", calender.MeetingLink).Replace("{organizer}", invitee).Replace("{ppnturl}", proposeNewDateAndTimeUrl).Replace("{invitee}", invitee)
                                    .Replace("{more}", "https://joble.app/").Replace("{aiName}", aiName).Replace("{aiImage}", aiImage).Replace("{viewurl}", generateAgenda).Replace("{meetingId}", calender.MeetingId);
                templateForSubsequentMeetings = template;
                template = template.Replace("{date}", calender.StartDate.ToShortDateString()).Replace("{time}", calender.StartDate.ToShortTimeString());

                for (var i = 1; i <= invitedMmemberNames.Count; i++)
                {
                    var tem = "{" + i + "}";
                    template = template.Replace(tem, invitedMmemberNames[i - 1]);
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, invitedMmemberNames[i - 1]);
                }

                for (var j = invitedMmemberNames.Count + 1; j <= 20; j++)
                {
                    var tem = "{" + j + "}";
                    template = template.Replace(tem, "");
                    templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, "");
                }

                if (attachment != null)
                {
                    for (var i = 1; i <= attachment.Count; i++)
                    {
                        var tem = "{attachmentURL" + i + "}";
                        template = template.Replace(tem, attachment[i - 1]);
                        templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, attachment[i - 1]);
                    }

                    for (var j = attachment.Count + 1; j <= 5; j++)
                    {
                        var tem = "{attachmentVisibility" + j + "}";
                        template = template.Replace(tem, "display: none;");
                        templateForSubsequentMeetings = templateForSubsequentMeetings.Replace(tem, "display: none;");
                    }
                }

                //subject = isAnUpdate ? $"Update - {subject}" : subject;
                if (icalenderStream != null)
                {
                    var icalender = new AttachmentDto
                    {
                        attachment = icalenderStream,
                        fileName = $"{calender.Name}.ics",
                    };

                    _emailService.SendEmailWithAttachments(template, user.Email, subject, new List<AttachmentDto> { icalender });
                    //BackgroundJob.Enqueue(() => _emailService.SendEmailWithAttachments(template, user.Email, subject, new List<AttachmentDto> { icalender }));
                }
                else
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, user.Email, subject));

                // Trigger background job that will send out meeting reminders to members
                var calenderVm = new CalenderVm
                {
                    Frequency = calender.Frequency,
                    SubDomain = subdomain,
                    StartDate = calender.StartDate,
                    NotifyMeInMinutes = calender.NotifyMembersIn
                };

                if (calenderVm.CustomFrequency != null)
                {
                    calenderVm.CustomFrequency = new CustomFrequencyDto
                    {
                        RepeatCount = calender.CustomFrequency.RepeatCount,
                        RepeatEvery = calender.CustomFrequency.RepeatEvery,
                        RepeatOn = calender.CustomFrequency.RepeatOn,
                        EndsAfter = calender.CustomFrequency.EndsAfter,
                        EndsOn = calender.CustomFrequency.EndsOn,
                        EndStatus = calender.CustomFrequency.EndStatus
                    };
                }

                if (!calender.IsCancelled)
                {
                    if (calender.Frequency == "OneOff")
                        ScheduleReminderForOneOffMeeting(calenderVm, calender.Id.ToString(), template, user.Email, calender.Name);
                    else
                    {
                        SendNotificationsForReoccuringMeetings(calenderVm, calender, template, user.Email);
                    }
                }

                // Send skill set email notification
                if (sendSkillMail)
                {
                    var skillTemplate = ReadTemplateFromFile("meeting_skills", _environment);
                    var skillSubject = $"Confirm Your Skills: {calender.Name}";
                    var link = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + "/confirm-skills?meetingId={1}&email={2}", subdomain, calender.Id.ToString(), user.Email) + $"?userId = {user.UserId}";

                    skillTemplate = skillTemplate.Replace("{name}", user.FirstName).Replace("{meeting_name}", calender.Name)
                        .Replace("{invitee}", invitee).Replace("{aiName}", aiName).Replace("{link}", link).Replace("{aiImage}", aiImage);

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(skillTemplate, user.Email, skillSubject));
                }
            }
        }

        private void SendBookingComfirmationEmail(SendMailRecord record)
        {
            var template = record.template.Replace("{name}", record.fullName).Replace("{message}", record.message).Replace("{meeting name}", record.externalMeeting.MeetingName).Replace("{location}", record.externalMeeting.Location).Replace("{duration}", record.externalMeeting.MeetingDuration.ToString()).Replace("{link}", record.externalMeeting.MeetingLink).Replace("{organizer}", record.invitee).Replace("{reshedule}", record.rescheduleLink).Replace("{cancel}", record.cancelLink);

            template = template.Replace("{date}", record.meetingDate.ToShortDateString()).Replace("{time}", record.meetingDate.ToShortTimeString());
            if (record.stream == null)
                BackgroundJob.Enqueue(() => _emailService.SendEmail(template, record.email, record.subject));
            else
                _emailService.SendEmailWithAttachments(template, record.email, record.subject, new List<AttachmentDto> { new AttachmentDto { attachment = record.stream, fileName = $"{record.externalMeeting.MeetingName}.ics" } }); // Send email with attachment (ics fileBackgroundJob.Enqueue(() => _emailService.SendEmail(template, record.email, record.subject));
        }

        #region
        /// <summary>
        /// Generate Meeting Id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private string GenerateMeetingId()
        {
            Random _random = new Random();
            string _chars = "abcdefghijklmnopqrstuvwxyz";


            char[] buffer = new char[11];
            for (int i = 0; i < 11; i++)
            {
                if (i == 3 || i == 7)
                {
                    buffer[i] = '-';
                    continue;
                }
                else
                    buffer[i] = _chars[_random.Next(_chars.Length)];
            }
            return new string(buffer);

        }
        #endregion

        #region
        /// <summary>
        /// Generate Meeting Link
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        private string GenerateMeetingLink(string subdomain, string meetingId)
        {
            string meetingLink = _meetingBaseURL + "/" + meetingId + "?d=" + subdomain;
            return meetingLink;
        }
        #endregion

        #endregion

        #region Private Methods - Notification
        private async Task SendOutBookingNotifications(BookExternalMeetingDto model, ExternalMeeting externalMeeting, UserProfile meetingOwner, BookedExternalMeeting bookedExternalMeet, bool reScheduled = false)
        {
            var inviteeName = meetingOwner.FirstName + " " + meetingOwner.LastName;
            externalMeeting.MeetingStartDateRange = model.SelectedDateAndTime;
            var bookedMeetingNotofication = new BookedMeetingNotificationDto
            {
                BookedMeetingId = bookedExternalMeet.Id.ToString(),
                Subdomain = model.SubDomain,
                FullName = model.FullName,
                Email = model.Email,
                GuestEmails = model.GuestEmails,
                ExternalMeeting = externalMeeting,
                MeetingOwner = meetingOwner,
                Subject = $"Meeting Invitation: {externalMeeting.MeetingName}",
                BookedMeetingSubject = $"Meeting Booked: {externalMeeting.MeetingName}",
                ExternalMemberEmails = model.ExternalMemberEmails,
                InternalMemberIds = model.InternalMemberIds,
            };

            //BackgroundJob.Enqueue(() => SendOutNotificationForBookedMeeting(bookedMeetingNotofication));
            SendOutNotificationForBookedMeeting(bookedMeetingNotofication);

            var meetingType = "";
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                meetingType = "Group";
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                meetingType = "Round Robin";
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
                meetingType = "One On One";

            // Send meeting reminder email to the user 30mins before the meeting
            if (externalMeeting.EmailRemindersBefore)
            {
                var bookedTime = bookedExternalMeet.SelectedDateAndTime;
                var template = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/booked_meeting_template_new.html"));
                var message = $"You have an upcomming One On One meeting with {meetingOwner.FirstName} {meetingOwner.LastName}";
                var subject = $"Meeting Reminder: {externalMeeting.MeetingName} Meeting";

                var sendMailRecord = new SendMailRecord(inviteeName, model.Email, template, subject, message, externalMeeting, model.SelectedDateAndTime, model.FullName, bookedExternalMeet.CancelMeetingLink, bookedExternalMeet.ReScheduleMeetingLink);

                var jobId = BackgroundJob.Schedule(() => _backGroundService.EmailReminderBefore(bookedExternalMeet.Id, bookedTime, model.SubDomain, sendMailRecord), bookedExternalMeet.SelectedDateAndTime.AddMinutes(-30));

                // Log the job id
                await LogJobId(bookedExternalMeet.Id.ToString(), jobId);
            }

            // Send a follow up email to the user 30mins after the meeting has held
            if (externalMeeting.EmailFollowUpAfter)
            {
                // This needs a template
                var bookedTime = bookedExternalMeet.SelectedDateAndTime;
                var template = Path.Combine(_environment.WebRootPath, @"EmailTemplates/booked_meeting_template_new.html");
                var message = $"Thank you for taking out time to meet with me, I hope you enjoyed the session as much as as I did. Please feel free to reach out to me if you need anything with respect to the session.";
                var subject = $"Follow-Up - {meetingType} Meeting";

                var sendMailRecord = new SendMailRecord(inviteeName, model.Email, template, subject, message, externalMeeting, model.SelectedDateAndTime, model.FullName, bookedExternalMeet.CancelMeetingLink, bookedExternalMeet.ReScheduleMeetingLink);
                var jobId = BackgroundJob.Schedule(() => _backGroundService.EmailReminderAfter(model.Email, externalMeeting, bookedExternalMeet, bookedTime, model.SubDomain, false), bookedExternalMeet.SelectedDateAndTime.AddMinutes(30));

                // Log the job id
                await LogJobId(bookedExternalMeet.Id.ToString(), jobId);
            }

            // Send an email comfirmation 30mins after booking
            if (externalMeeting.EmailComfirmation)
            {
                var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/booked_meeting_template_new.html");
                var template = File.ReadAllText(templatePath);
                var message = $"You have successfully booked a {meetingType} meeting with {meetingOwner.FirstName} {meetingOwner.LastName}";
                var subject = !reScheduled ? $"Booking Confirmation: {externalMeeting.MeetingName}" : $"ReShchedule Confirmation: {externalMeeting.MeetingName}";

                // Send an iCalender file to the invitees and the meeting owner
                var env = new CalenderEventDto
                {
                    Title = externalMeeting.MeetingName,
                    Description = externalMeeting.MeetingName,
                    Location = externalMeeting.Location,
                    StartDateAndTime = externalMeeting.MeetingStartDateRange.Value,
                    EndDateAndTime = externalMeeting.MeetingStartDateRange.Value.AddMinutes(externalMeeting.MeetingDuration),
                    Attendees = new Dictionary<string, string>(),
                    Organizer = meetingOwner,
                    MeetingId = bookedExternalMeet.Id.ToString(),
                    MeetingLink = externalMeeting.MeetingLink,
                    subdomain = model.SubDomain,
                    Frequency = "OneOff",
                    CustomFrequencyDto = null
                };

                // Add the organizer to the attendees
                if (!env.Attendees.ContainsKey(env.Organizer.Email))
                    env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);

                var icalenderStream = CalenderHelper.CreateCalenderEvent(env);
                var sendMailRecord = new SendMailRecord(inviteeName, model.Email, template, subject, message, externalMeeting, model.SelectedDateAndTime, model.FullName, bookedExternalMeet.CancelMeetingLink, bookedExternalMeet.ReScheduleMeetingLink, icalenderStream);

                SendBookingComfirmationEmail(sendMailRecord);
            }
        }

        #endregion

        #region Private Methods
        private async Task<List<ExternalMeetingTimeManagement>> ComputeTimeBreakDownForGroupUpdate(
            string externalMeetingId,
            ExternalMeetingDto updateExternalMeeting,
            ExternalMeeting externalMeeting,
            List<PersonalSchedule> personalSchs)
        {
            var externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();
            if (updateExternalMeeting.TeamMembersIds != null && updateExternalMeeting.TeamMembersIds.Any())
            {
                foreach (var hostId in updateExternalMeeting.TeamMembersIds)
                {
                    var schedules = await Db.PersonalSchedule
                        .Where(x => x.ScheduleName == updateExternalMeeting.PersonalSchedule && x.UserId.ToString() == hostId)
                        .ToListAsync();

                    if (schedules != null && schedules.Any())
                        personalSchs.AddRange(schedules);
                }
            }

            // 1. Fetch all existing ExternalMeetingTimeManagement entries for this ExternalMeeting
            var existingEntries = await Db.ExternalMeetingTimeManagements
                .Where(x => x.ExternalMeetingId.ToString() == externalMeetingId)
                .ToListAsync();

            // 2. Cache selected time slots by PersonalScheduleId
            var selectedSlotsBySchedule = existingEntries
                .Where(e => !string.IsNullOrWhiteSpace(e.SelectedTimeSlots))
                .GroupBy(e => e.PersonalScheduleId)
                .ToDictionary(
                    g => g.Key,
                    g => g.SelectMany(e => e.SelectedTimeSlots.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()))
                          .ToHashSet()
                );

            // 3. Remove all existing records (we'll recreate fresh)
            if (existingEntries.Any())
                Db.ExternalMeetingTimeManagements.RemoveRange(existingEntries);

            // Group personal schedules by DayOfWeek string
            var groupedByDay = personalSchs.GroupBy(p => p.Day);

            var startDate = updateExternalMeeting.CanBookStartDate.Date;
            var endDate = updateExternalMeeting.CanBookEndDate.Date;

            foreach (var group in groupedByDay)
            {
                var dayOfWeek = group.Key;
                var schedules = group.ToList();
                var dayEnum = Enum.Parse<DayOfWeek>(dayOfWeek);

                // Iterate through each matching date in booking range
                for (var currentDate = startDate; currentDate <= endDate; currentDate = currentDate.AddDays(1))
                {
                    if (currentDate.DayOfWeek != dayEnum)
                        continue;

                    // 4. Compute slots per schedule for this day
                    var slotsPerSchedule = new Dictionary<Guid, HashSet<string>>();

                    foreach (var schedule in schedules)
                    {
                        var slots = new HashSet<string>();
                        var time = schedule.StartTime;

                        while (time < schedule.EndTime.AddMinutes(-updateExternalMeeting.MeetingDuration))
                        {
                            slots.Add(time.ToString("HH:mm"));
                            time = time.AddMinutes(updateExternalMeeting.MeetingDuration + updateExternalMeeting.MeetingBuffer);
                        }

                        slotsPerSchedule[schedule.Id] = slots;
                    }

                    // 5. Compute overlapping slots across all schedules on this day
                    var overlappingSlots = slotsPerSchedule.Values
                        .Aggregate((acc, set) =>
                        {
                            acc.IntersectWith(set);
                            return acc;
                        });

                    // 6. For each schedule, remove selected slots and create new records
                    foreach (var schedule in schedules)
                    {
                        var freeSlots = new HashSet<string>(overlappingSlots);

                        if (selectedSlotsBySchedule.TryGetValue(schedule.Id, out var selectedSlots))
                        {
                            freeSlots.ExceptWith(selectedSlots);
                        }

                        externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            UserId = schedule.UserId.ToString(),
                            ScheduleName = updateExternalMeeting.PersonalSchedule.ToString(),
                            PersonalScheduleId = schedule.Id,
                            TimeBreakDown = string.Join(",", freeSlots.OrderBy(t => t)),
                            DayOfTheWeek = schedule.Day,
                            Date = currentDate
                        });
                    }
                }
            }

            await Db.ExternalMeetingTimeManagements.AddRangeAsync(externalMeetingTimeManagements);
            return externalMeetingTimeManagements;
        }

        private async Task<List<ExternalMeetingTimeManagement>> ComputeTimeBreakDownForOonOAndRRUpdate(string externalMeetingId, ExternalMeetingDto updateExternalMeeting, ExternalMeeting externalMeeting, List<PersonalSchedule> personalSchs, List<string>? otherMeetingHostsIds = null)
        {
            var externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();
            if (otherMeetingHostsIds != null && otherMeetingHostsIds.Any())
            {
                // Get personal schedules for the other meeting hosts and add them to the list
                foreach (var hostId in otherMeetingHostsIds)
                {
                    var personalSchedule = await Db.PersonalSchedule
                        .Where(x => x.ScheduleName == updateExternalMeeting.PersonalSchedule && x.UserId.ToString() == hostId).ToListAsync();
                    if (personalSchedule != null)
                    {
                        personalSchs.AddRange(personalSchedule);
                    }
                }
            }

            foreach (var personalSch in personalSchs)
            {
                var timeBreakDowns = new List<string>();
                var timeBreakDown = personalSch.StartTime;

                while (timeBreakDown < personalSch.EndTime.AddMinutes(-updateExternalMeeting.MeetingDuration))
                {
                    timeBreakDown = timeBreakDown.AddMinutes(updateExternalMeeting.MeetingDuration + updateExternalMeeting.MeetingBuffer);
                    timeBreakDowns.Add(timeBreakDown.ToShortTimeString());
                }

                var existingExternalMeetingTimeManagements = await Db.ExternalMeetingTimeManagements
                    .Where(x => x.ExternalMeetingId.ToString() == externalMeetingId && x.PersonalScheduleId == personalSch.Id).ToListAsync();
                if (existingExternalMeetingTimeManagements.Any())
                {
                    foreach (var existingExternalMeetingTimeManagement in existingExternalMeetingTimeManagements)
                    {
                        if (existingExternalMeetingTimeManagement.Date.Value > updateExternalMeeting.CanBookEndDate.Date)
                        {
                            Db.ExternalMeetingTimeManagements.Remove(existingExternalMeetingTimeManagement);
                            continue;
                        }

                        if (existingExternalMeetingTimeManagement != null && existingExternalMeetingTimeManagement.SelectedTimeSlots != null)
                        {
                            var selectedTimeSlots = existingExternalMeetingTimeManagement.SelectedTimeSlots.Split(",");
                            timeBreakDowns = timeBreakDowns.Where(x => !selectedTimeSlots.Contains(x)).ToList();

                            existingExternalMeetingTimeManagement.TimeBreakDown = string.Join(",", timeBreakDowns);
                            Db.ExternalMeetingTimeManagements.Update(existingExternalMeetingTimeManagement);
                        }
                        else
                        {
                            existingExternalMeetingTimeManagement.TimeBreakDown = string.Join(",", timeBreakDowns);
                            Db.ExternalMeetingTimeManagements.Update(existingExternalMeetingTimeManagement);
                        }
                    }

                    var currentDate = externalMeeting.CanBookEndDate.Value.Date.AddDays(1);
                    var endDate = updateExternalMeeting.CanBookEndDate.Date;
                    var targetDayOfWeek = Enum.Parse<DayOfWeek>(personalSch.Day); // assuming personalSch.Day is "Monday", "Tuesday", etc.

                    while (currentDate <= endDate)
                    {
                        if (currentDate.DayOfWeek == targetDayOfWeek)
                        {
                            externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                            {
                                ExternalMeetingId = externalMeeting.Id,
                                UserId = externalMeeting.CreatedBy.ToString(),
                                ScheduleName = updateExternalMeeting.PersonalSchedule.ToString(),
                                PersonalScheduleId = personalSch.Id,
                                TimeBreakDown = string.Join(",", timeBreakDowns),
                                DayOfTheWeek = personalSch.Day,
                                Date = currentDate // if you have a Date field in your model
                            });
                        }

                        currentDate = currentDate.AddDays(1);
                    }
                }
                else
                {
                    // Get all dates matching the personal schedule's DayOfWeek till CanBookEndDate
                    var currentDate = updateExternalMeeting.CanBookStartDate.Date;
                    var endDate = updateExternalMeeting.CanBookEndDate.Date;
                    var targetDayOfWeek = Enum.Parse<DayOfWeek>(personalSch.Day); // assuming personalSch.Day is "Monday", "Tuesday", etc.

                    while (currentDate <= endDate)
                    {
                        if (currentDate.DayOfWeek == targetDayOfWeek)
                        {
                            externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                            {
                                ExternalMeetingId = externalMeeting.Id,
                                UserId = externalMeeting.CreatedBy.ToString(),
                                ScheduleName = updateExternalMeeting.PersonalSchedule.ToString(),
                                PersonalScheduleId = personalSch.Id,
                                TimeBreakDown = string.Join(",", timeBreakDowns),
                                DayOfTheWeek = personalSch.Day,
                                Date = currentDate // if you have a Date field in your model
                            });
                        }

                        currentDate = currentDate.AddDays(1);
                    }
                }
            }

            return externalMeetingTimeManagements;
        }

        private async Task<List<ExternalMeetingTimeManagement>> ComputeTimeBreakDownsForGroup(
            ExternalMeetingDto externalMeetingDto,
            ExternalMeeting externalMeeting,
            List<PersonalSchedule> personalSchs)
        {
            // Load all schedules for all hosts
            if (externalMeetingDto.TeamMembersIds != null && externalMeetingDto.TeamMembersIds.Any())
            {
                foreach (var hostId in externalMeetingDto.TeamMembersIds)
                {
                    var schedules = await Db.PersonalSchedule
                        .Where(x => x.ScheduleName == externalMeetingDto.PersonalSchedule && x.UserId.ToString() == hostId)
                        .ToListAsync();

                    if (schedules != null)
                        personalSchs.AddRange(schedules);
                }
            }

            var externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();

            var currentDate = externalMeeting.CanBookStartDate.Value.Date;
            var endDate = externalMeeting.CanBookEndDate.Value.Date;

            while (currentDate <= endDate)
            {
                var dayOfWeek = currentDate.DayOfWeek.ToString(); // e.g., "Monday"
                var schedulesForDay = personalSchs
                    .Where(p => p.Day.Equals(dayOfWeek, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                if (!schedulesForDay.Any())
                {
                    currentDate = currentDate.AddDays(1);
                    continue;
                }

                // Generate time slots per person
                var slotsPerUser = schedulesForDay.Select(schedule =>
                {
                    var slots = new List<TimeSpan>();
                    var time = schedule.StartTime;

                    while (time < schedule.EndTime.AddMinutes(-externalMeetingDto.MeetingDuration))
                    {
                        slots.Add(time.TimeOfDay);
                        time = time.AddMinutes(externalMeetingDto.MeetingDuration + externalMeetingDto.MeetingBuffer);
                    }

                    return slots;
                }).ToList();

                // Find common time slots (intersection)
                var overlappingSlots = slotsPerUser
                    .Skip(1)
                    .Aggregate(new HashSet<TimeSpan>(slotsPerUser.First()), (h, e) =>
                    {
                        h.IntersectWith(e);
                        return h;
                    });

                var usersGrouped = schedulesForDay.GroupBy(s => s.UserId);
                foreach (var userGroup in usersGrouped)
                {
                    var userId = userGroup.Key;
                    var slots = new List<TimeSpan>();

                    foreach (var schedule in userGroup)
                    {
                        var time = schedule.StartTime;

                        while (time < schedule.EndTime.AddMinutes(-externalMeetingDto.MeetingDuration))
                        {
                            slots.Add(time.TimeOfDay);
                            time = time.AddMinutes(externalMeetingDto.MeetingDuration + externalMeetingDto.MeetingBuffer);
                        }
                    }

                    var distinctOrderedSlots = slots.Distinct().OrderBy(s => s).ToList();

                    if (distinctOrderedSlots.Any())
                    {
                        var timeBreakDownString = string.Join(",", distinctOrderedSlots.Select(s => s.ToString(@"hh\:mm")));

                        externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            UserId = userId.ToString(),
                            ScheduleName = externalMeetingDto.PersonalSchedule.ToString(),
                            TimeBreakDown = timeBreakDownString,
                            DayOfTheWeek = dayOfWeek,
                            Date = currentDate,
                            PersonalScheduleId = userGroup.First().Id // or choose based on logic
                        });
                    }
                }

                currentDate = currentDate.AddDays(1);
            }

            return externalMeetingTimeManagements;
        }

        private async Task<List<ExternalMeetingTimeManagement>> ComputeTimeBreakDownsForRoundRobin(ExternalMeetingDto externalMeetingDto, ExternalMeeting externalMeeting, List<PersonalSchedule> personalSchs, List<string> otherMeetingHostsIds)
        {
            var externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();

            // Get personal schedules for the other meeting hosts and add them to the list
            foreach (var hostId in otherMeetingHostsIds)
            {
                var personalSchedule = await Db.PersonalSchedule
                    .Where(x => x.ScheduleName == externalMeetingDto.PersonalSchedule && x.UserId.ToString() == hostId).ToListAsync();
                if (personalSchedule != null)
                {
                    personalSchs.AddRange(personalSchedule);
                }
            }

            foreach (var personalSch in personalSchs)
            {
                var timeBreakDowns = new List<string>();
                var timeBreakDown = personalSch.StartTime;
                timeBreakDowns.Add(timeBreakDown.ToShortTimeString());

                while (timeBreakDown < personalSch.EndTime.AddMinutes(-externalMeetingDto.MeetingDuration))
                {
                    timeBreakDown = timeBreakDown.AddMinutes(externalMeetingDto.MeetingDuration + externalMeetingDto.MeetingBuffer);
                    timeBreakDowns.Add(timeBreakDown.ToShortTimeString());
                }

                // Get all dates matching the personal schedule's DayOfWeek till CanBookEndDate
                var currentDate = externalMeeting.CanBookStartDate.Value.Date;
                var endDate = externalMeeting.CanBookEndDate.Value.Date;
                var targetDayOfWeek = Enum.Parse<DayOfWeek>(personalSch.Day); // assuming personalSch.Day is "Monday", "Tuesday", etc.

                while (currentDate <= endDate)
                {
                    if (currentDate.DayOfWeek == targetDayOfWeek)
                    {
                        externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            UserId = externalMeeting.CreatedBy.ToString(),
                            ScheduleName = externalMeetingDto.PersonalSchedule.ToString(),
                            PersonalScheduleId = personalSch.Id,
                            TimeBreakDown = string.Join(",", timeBreakDowns),
                            DayOfTheWeek = personalSch.Day,
                            Date = currentDate // if you have a Date field in your model
                        });
                    }

                    currentDate = currentDate.AddDays(1);
                }
            }

            return externalMeetingTimeManagements;
        }

        private async Task<List<ExternalMeetingTimeManagement>> ComputeTimeBreakDownsForOneOnOne(ExternalMeetingDto externalMeetingDto, ExternalMeeting externalMeeting, List<PersonalSchedule> personalSchs)
        {
            var externalMeetingTimeManagements = new List<ExternalMeetingTimeManagement>();
            foreach (var personalSch in personalSchs)
            {
                var timeBreakDowns = new List<string>();
                var timeBreakDown = personalSch.StartTime;
                timeBreakDowns.Add(timeBreakDown.ToShortTimeString());

                while (timeBreakDown < personalSch.EndTime.AddMinutes(-externalMeetingDto.MeetingDuration))
                {
                    timeBreakDown = timeBreakDown.AddMinutes(externalMeetingDto.MeetingDuration + externalMeetingDto.MeetingBuffer);
                    timeBreakDowns.Add(timeBreakDown.ToShortTimeString());
                }

                // Get all dates matching the personal schedule's DayOfWeek till CanBookEndDate
                var currentDate = externalMeeting.CanBookStartDate.Value.Date;
                var endDate = externalMeeting.CanBookEndDate.Value.Date;
                var targetDayOfWeek = Enum.Parse<DayOfWeek>(personalSch.Day); // assuming personalSch.Day is "Monday", "Tuesday", etc.

                while (currentDate <= endDate)
                {
                    if (currentDate.DayOfWeek == targetDayOfWeek)
                    {
                        externalMeetingTimeManagements.Add(new ExternalMeetingTimeManagement
                        {
                            ExternalMeetingId = externalMeeting.Id,
                            UserId = externalMeeting.CreatedBy.ToString(),
                            ScheduleName = externalMeetingDto.PersonalSchedule.ToString(),
                            PersonalScheduleId = personalSch.Id,
                            TimeBreakDown = string.Join(",", timeBreakDowns),
                            DayOfTheWeek = personalSch.Day,
                            Date = currentDate // if you have a Date field in your model
                        });
                    }

                    currentDate = currentDate.AddDays(1);
                }
            }

            return externalMeetingTimeManagements;
        }

        private async Task ProcessMeetingParticipants(CalenderMeeting meet)
        {
            var userMeetings = Db.UserIdMeetingIds.Where(x => x.CalenderId == meet.Id.ToString()).ToList();

            var meetingOwner = Db.UserProfiles
                   .Where(x => x.UserId == meet.CreatedBy.ToString())
                   .Select(x => new UserMDVm
                   {
                       FirstName = x.FirstName,
                       LastName = x.LastName,
                       Email = x.Email,
                       ProfilePictureUrl = x.ProfilePictureUrl,
                       Id = x.UserId
                   }).FirstOrDefault();

            if (meetingOwner.ProfilePictureUrl is not null)
                meetingOwner.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(meetingOwner?.ProfilePictureUrl);

            var members = userMeetings.Select(x =>
            {
                var user = Db.UserProfiles.FirstOrDefault(y => y.UserId == x.UserId);
                if (user != null)
                {
                    return new UserMDVm
                    {
                        Id = user.UserId,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        ProfilePictureUrl = !string.IsNullOrEmpty(user.ProfilePictureUrl) ? _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl).Result : null
                    };
                }

                return new UserMDVm();
            }).ToList();

            meet.ExternalMembers = userMeetings
                .Where(x => x.Email != null && x.UserId == null)
                .Select(x => x.Email).ToList();

            meet.Members = members.Where(m => m.Id != null || m.Email != null).ToList();
            meet.MeetingOwner = meetingOwner;
        }

        private void PayloadValidations(ExternalMeeting externalMeeting, BookExternalMeetingDto model)
        {
            if (externalMeeting.IsLocked)
                throw new OperationNotAllowedException("This meeting or event is locked");
            if (externalMeeting.IsCancelled)
                throw new OperationNotAllowedException("This meeting has been canceled");

            // Check if within the allowed dates
            if (model.SelectedDateAndTime.Date < externalMeeting.CanBookStartDate.Value.Date)
                throw new DirtyFormException($"The selected date is less than the allowed booking start date. " +
                    $"This meeting allowed booking date starts on {externalMeeting.CanBookStartDate.Value.ToShortDateString()} from {externalMeeting.CanBookStartDate.Value.ToShortTimeString()}");

            if (model.SelectedDateAndTime.Date > externalMeeting.CanBookEndDate.Value.Date)
                throw new DirtyFormException($"The selected date is greater than the allowed booking end date. " +
                    $"This meeting allowed booking end date is on {externalMeeting.CanBookEndDate.Value.ToShortDateString()} from {externalMeeting.CanBookEndDate.Value.ToShortTimeString()}");

            if (GetAdjustedDateTimeBasedOnTZNow() > externalMeeting.CanBookEndDate)
                throw new DirtyFormException("This meeting or event has expired");

            externalMeeting.MaxInvitePerMeeting = externalMeeting.MaxInvitePerMeeting == 0 ? 3 : externalMeeting.MaxInvitePerMeeting == null ? 3 : externalMeeting.MaxInvitePerMeeting;
            if (model.GuestEmails?.Count() > externalMeeting.MaxInvitePerMeeting)
                throw new DirtyFormException($"You can only invite {externalMeeting.MaxInvitePerMeeting} guest(s) to this meeting");

            // Check if the creator of the meeting allowed for guest to be added when booking
            if (!externalMeeting.ExternalMeetingQuestion.AddGuests && model.GuestEmails.Any())
                throw new DirtyFormException("This meeting or event does not allow guest to be added when booking");

            // Check that the time selected has not reached the max number of bookings for group meeting type
            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                var bookedMeetings = Db.BookedExternalMeeting
                    .Where(x => x.ExternalMeetingId == model.ExternalMeetingId && x.SelectedDateAndTime.TimeOfDay == model.SelectedDateAndTime.TimeOfDay)
                    .ToList();
                if (bookedMeetings.Count() >= externalMeeting.MaxInvitePerMeeting)
                    throw new DirtyFormException($"The selected date and time has reached the maximum number of bookings for this meeting. " +
                        $"Please select another date or time or contact the meeting organizer");
            }
        }

        private void SendOutNotificationForBookedMeeting(BookedMeetingNotificationDto bookedMeetingNotificationDto)
        {
            var inviteeName = bookedMeetingNotificationDto.MeetingOwner.FirstName + " " + bookedMeetingNotificationDto.MeetingOwner.LastName;

            // Send out invitation email to the guests
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/meeting_template_new.html");
            var templateFromFolder = File.ReadAllText(templatePath);
            var calenderMeeting = new CalenderMeeting
            {
                Id = bookedMeetingNotificationDto.ExternalMeeting.Id,
                Location = bookedMeetingNotificationDto.ExternalMeeting.Location,
                MeetingDuration = bookedMeetingNotificationDto.ExternalMeeting.MeetingDuration.ToString(),
                StartDate = bookedMeetingNotificationDto.ExternalMeeting.MeetingStartDateRange.Value,
                MeetingLink = bookedMeetingNotificationDto.ExternalMeeting.MeetingLink,
                Name = bookedMeetingNotificationDto.ExternalMeeting.MeetingName,
                IsCancelled = true,
            };

            var invitedTeamMemberNames = new List<string>();
            var invitedUserProfiles = new List<UserProfile>();

            // Send an iCalender file to the invitees and the meeting owner
            MemoryStream icalenderStream = null;
            var env = new CalenderEventDto
            {
                Title = bookedMeetingNotificationDto.ExternalMeeting.MeetingName,
                Description = bookedMeetingNotificationDto.ExternalMeeting.MeetingName,
                Location = bookedMeetingNotificationDto.ExternalMeeting.Location,
                StartDateAndTime = bookedMeetingNotificationDto.ExternalMeeting.MeetingStartDateRange.Value,
                EndDateAndTime = bookedMeetingNotificationDto.ExternalMeeting.MeetingStartDateRange.Value.AddMinutes(bookedMeetingNotificationDto.ExternalMeeting.MeetingDuration),
                Attendees = new Dictionary<string, string>(),
                Organizer = bookedMeetingNotificationDto.MeetingOwner,
                MeetingId = bookedMeetingNotificationDto.BookedMeetingId,
                MeetingLink = bookedMeetingNotificationDto.ExternalMeeting.MeetingLink,
                subdomain = bookedMeetingNotificationDto.Subdomain,
                Frequency = "OneOff",
                CustomFrequencyDto = null
            };

            // Add the organizer to the attendees
            if (!env.Attendees.ContainsKey(env.Organizer.Email))
                env.Attendees.Add(env.Organizer.Email, env.Organizer.UserId);

            // Send booked meeting notification to the creator of the meeting
            var aiName = Utility.Constants.AI_DEFAULT_NAME;
            var aiImage = Utility.Constants.AI_DEFAULT_IMAGE;
            if (bookedMeetingNotificationDto.ExternalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
            {
                env.Attendees.Add(bookedMeetingNotificationDto.Email, null);
                icalenderStream = CalenderHelper.CreateCalenderEvent(env);
                var message = string.Format("{0} has booked a meeting with you.", bookedMeetingNotificationDto.FullName);

                SendMailToInternalMembers(bookedMeetingNotificationDto.Subdomain, inviteeName, bookedMeetingNotificationDto.MeetingOwner.Email, calenderMeeting, templateFromFolder, new List<string> { }, new List<UserProfile> { bookedMeetingNotificationDto.MeetingOwner }, false, bookedMeetingNotificationDto.BookedMeetingSubject, message, aiName, aiImage, null, false, icalenderStream);
            }

            if (bookedMeetingNotificationDto.ExternalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                var otherMeetingHostIds = bookedMeetingNotificationDto.ExternalMeeting.OtherMeetingHostsIds?.Split(",");
                if (otherMeetingHostIds is not null)
                {
                    foreach (var hostId in otherMeetingHostIds)
                    {
                        var host = Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == hostId).Result;
                        invitedUserProfiles.Add(host);
                        invitedTeamMemberNames.Add(host.FirstName + " " + host.LastName);
                        env.Attendees.Add(host.Email, host.UserId);
                    }
                }

                var message = string.Format("{0} has booked a Round Robin meeting which you are a potential guest to.", bookedMeetingNotificationDto.FullName);
                var potentialHostsProfile = new List<UserProfile>() { bookedMeetingNotificationDto.MeetingOwner };
                potentialHostsProfile.AddRange(invitedUserProfiles);
                invitedTeamMemberNames.AddRange(bookedMeetingNotificationDto.GuestEmails);
                invitedTeamMemberNames.Add(inviteeName);

                icalenderStream = CalenderHelper.CreateCalenderEvent(env);
                SendMailToInternalMembers(bookedMeetingNotificationDto.Subdomain, inviteeName, bookedMeetingNotificationDto.MeetingOwner.Email, calenderMeeting, templateFromFolder, invitedTeamMemberNames, potentialHostsProfile, false, bookedMeetingNotificationDto.BookedMeetingSubject, message, aiName, aiImage, null, false, icalenderStream);

                if (bookedMeetingNotificationDto.GuestEmails.Any())
                {
                    bookedMeetingNotificationDto.GuestEmails.ForEach(x =>
                    {
                        env.Attendees.Add(x, null);
                    });
                    message = string.Format("{0} has invited you to a meeting.", bookedMeetingNotificationDto.FullName);

                    SendMailToExternalMembers(bookedMeetingNotificationDto.Subdomain, bookedMeetingNotificationDto.FullName, bookedMeetingNotificationDto.Email, calenderMeeting, templateFromFolder, invitedTeamMemberNames, false, bookedMeetingNotificationDto.GuestEmails, bookedMeetingNotificationDto.Subject, message, aiName, aiImage, inviteeName, null, false, icalenderStream);
                }
            }

            if (bookedMeetingNotificationDto.ExternalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                // Get meeting members
                var invitedMemberNames = new List<string>();
                var meetingMembers = Db.ExternalMeetingMembers
                    .Where(x => x.ExternalMeetingId == bookedMeetingNotificationDto.ExternalMeeting.Id.ToString())
                    .Select(x => x.UserId)
                    .ToList();
                foreach (var member in meetingMembers)
                {
                    if (member is null) continue;
                    var memberProfile = Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == member).Result;
                    invitedUserProfiles.Add(memberProfile);
                    invitedMemberNames.Add(memberProfile.FirstName + " " + memberProfile.LastName);
                    env.Attendees.Add(memberProfile.Email, memberProfile.UserId);
                }

                invitedUserProfiles.Add(bookedMeetingNotificationDto.MeetingOwner);
                invitedMemberNames.Add(inviteeName);
                invitedMemberNames.AddRange(bookedMeetingNotificationDto.GuestEmails);

                icalenderStream = CalenderHelper.CreateCalenderEvent(env);
                var message = string.Format("{0} has booked a Group meeting which you are a potential guest to.", bookedMeetingNotificationDto.FullName);
                SendMailToInternalMembers(bookedMeetingNotificationDto.Subdomain, inviteeName, bookedMeetingNotificationDto.MeetingOwner.Email, calenderMeeting, templateFromFolder, invitedMemberNames, invitedUserProfiles, false, bookedMeetingNotificationDto.BookedMeetingSubject, message, aiName, aiImage, null, false, icalenderStream);

                if (bookedMeetingNotificationDto.GuestEmails.Any())
                {
                    message = string.Format("{0} has invited you to a meeting.", bookedMeetingNotificationDto.FullName);

                    SendMailToExternalMembers(bookedMeetingNotificationDto.Subdomain, bookedMeetingNotificationDto.FullName, bookedMeetingNotificationDto.Email, calenderMeeting, templateFromFolder, invitedMemberNames, false, bookedMeetingNotificationDto.GuestEmails, bookedMeetingNotificationDto.Subject, message, aiName, aiImage, inviteeName, null, false, icalenderStream);
                }
            }

            if (bookedMeetingNotificationDto.ExternalMemberEmails.Any() || bookedMeetingNotificationDto.InternalMemberIds.Any())
            {
                foreach (var member in bookedMeetingNotificationDto.InternalMemberIds)
                {
                    if (member is null) continue;
                    var memberProfile = Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == member).Result;
                    invitedUserProfiles.Add(memberProfile);
                    invitedTeamMemberNames.Add(memberProfile.FirstName + " " + memberProfile.LastName);
                    env.Attendees.Add(memberProfile.Email, memberProfile.UserId);
                }

                invitedUserProfiles.Add(bookedMeetingNotificationDto.MeetingOwner);
                invitedTeamMemberNames.Add(inviteeName);
                invitedTeamMemberNames.AddRange(bookedMeetingNotificationDto.ExternalMemberEmails);

                var newMessage = string.Format("{0} has invited you to a meeting.", inviteeName);
                if (bookedMeetingNotificationDto.InternalMemberIds.Any())
                {
                    SendMailToInternalMembers(bookedMeetingNotificationDto.Subdomain, inviteeName, bookedMeetingNotificationDto.MeetingOwner.Email, calenderMeeting, templateFromFolder, invitedTeamMemberNames, invitedUserProfiles, false, bookedMeetingNotificationDto.BookedMeetingSubject, newMessage, aiName, aiImage, null, false, icalenderStream);
                }

                if (bookedMeetingNotificationDto.ExternalMemberEmails.Any())
                {
                    bookedMeetingNotificationDto.ExternalMemberEmails.ForEach(x =>
                    {
                        env.Attendees.Add(x, null);
                    });
                    SendMailToExternalMembers(bookedMeetingNotificationDto.Subdomain, inviteeName, bookedMeetingNotificationDto.Email, calenderMeeting, templateFromFolder, invitedTeamMemberNames, false, bookedMeetingNotificationDto.ExternalMemberEmails, bookedMeetingNotificationDto.Subject, newMessage, aiName, aiImage, inviteeName, null, false, icalenderStream);
                }
            }
        }

        private void SendNotifications(ExternalMeetingDto externalMeetingDto, string invitee, string inviteeEmail, ExternalMeeting externalMeeting, string templateFromFolder)
        {
            var calenderMeeting = new CalenderMeeting
            {
                Id = externalMeeting.Id,
                Location = externalMeeting.Location,
                MeetingDuration = externalMeeting.MeetingDuration.ToString(),
                StartDate = externalMeeting.MeetingStartDateRange.Value,
                MeetingLink = externalMeeting.MeetingLink,
                Name = externalMeeting.MeetingName,
                IsCancelled = true,
            };

            if (externalMeetingDto.InvitedGuestEmails.Any())
            {
                var meetingType = "";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
                    meetingType = "Group";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
                    meetingType = "Round Robin";
                if (externalMeeting.ExternalMeetingType == ExternalMeetingType.OneOnOne)
                    meetingType = "One On One";

                var templatePathForExternalMeetingInvitation = Path.Combine(_environment.WebRootPath, @"EmailTemplates/external-meeting-invitation.html");
                var template = File.ReadAllText(templatePathForExternalMeetingInvitation);
                template = template.Replace("{meetingType}", meetingType).Replace("{invitee}", invitee).Replace("{url}", externalMeeting.BookingLink).Replace("{name}", externalMeeting.MeetingName);

                BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(template, externalMeetingDto.InvitedGuestEmails, $"Meeting Invitation - {externalMeeting.MeetingName}"));
            }
            var aiName = Utility.Constants.AI_DEFAULT_NAME;
            var aiImage = Utility.Constants.AI_DEFAULT_IMAGE;

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.RoundRobin)
            {
                if (externalMeetingDto.OtherMeetingHostsIds.Any())
                {
                    var otherMeetingHostsName = new List<string>();
                    _cache.TryGetValue(externalMeeting.MeetingId, out List<UserProfile> otherMeetingHosts);
                    _cache.Remove(externalMeeting.MeetingId);

                    foreach (var host in otherMeetingHosts)
                        otherMeetingHostsName.Add(host.FirstName + " " + host.LastName);

                    otherMeetingHostsName.AddRange(externalMeetingDto.InvitedGuestEmails);

                    // Send email to the other hosts
                    var message = string.Format("You have been invited as a potential host to a Round Robin meeting by {0}", invitee);
                    SendMailToInternalMembers(externalMeetingDto.SubDomain, invitee, inviteeEmail, calenderMeeting, templateFromFolder, otherMeetingHostsName, otherMeetingHosts, false, $"Meeting Invitation - {externalMeeting.MeetingName}", message, aiName, aiImage);
                }
            }

            if (externalMeeting.ExternalMeetingType == ExternalMeetingType.Group)
            {
                var inviteeEmails = new List<string>();
                var message = string.Format("{0} has invited you to a meeting.", invitee);

                if (externalMeetingDto.TeamMembersIds.Any())
                {
                    var invitedInternalMembersNames = new List<string>();
                    var invitedInternalMembers = Db.UserProfiles.Where(x => externalMeetingDto.TeamMembersIds.Contains(x.UserId))
                        .ToListAsync().Result;

                    foreach (var host in invitedInternalMembers)
                        invitedInternalMembersNames.Add(host.FirstName + " " + host.LastName);

                    invitedInternalMembersNames.AddRange(externalMeetingDto.InvitedGuestEmails);

                    SendMailToInternalMembers(externalMeetingDto.SubDomain, invitee, inviteeEmail, calenderMeeting, templateFromFolder, invitedInternalMembersNames, invitedInternalMembers, false, $"Meeting Invitation - {externalMeeting.MeetingName}", message, aiName, aiImage);
                }
            }
        }

        private async Task<string> AddNotification(AddNotificationDto model)
        {
            var notification = new Notification.Models.Notification
            {
                Message = model.Message,
                Event = model.Event,
                EventId = model.EventId,
                CreatedAt = GetAdjustedDateTimeBasedOnTZNow(),
                CreatedBy = model.CreatedBy,
            };

            await Db.Notifications.AddAsync(notification);
            return notification.Id.ToString();
        }

        private async Task<bool> AddUserNotification(List<string> userIds, Guid notificationId)
        {
            var userProfileId = await Db.UserProfiles.Where(x => userIds.Contains(x.UserId))
                .Select(x => x.Id.ToString()).ToListAsync();

            var userNotifications = new List<UserNotification>();
            foreach (var userId in userProfileId)
            {
                userNotifications.Add(new UserNotification
                {
                    UserProfileId = userId,
                    NotificationId = notificationId
                });
            }

            Db.UserNotifications.AddRange(userNotifications);
            return true;
        }

        private async Task<(string, string)> GetAiDetails(string token)
        {
            return (Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
            var headers = new Dictionary<string, string>()
                {
                    { "Authorization", token }
                };

            var aiCharacter = await _apiCallService.MakeApiCallGenericAsync<AICharacterDto, AICharacterDto>(Utility.Constants.AI_DETAILS_BASEURL, "", Method.Get, null, headers);
            if (aiCharacter != null && aiCharacter.success)
            {
                return (aiCharacter?.data.name ?? Utility.Constants.AI_DEFAULT_NAME, aiCharacter?.data.image_url ?? Utility.Constants.AI_DEFAULT_IMAGE);
            }
            else
                return (Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
        }

        #endregion

        #region Cancel External Meeting
        public async Task<bool> CancelExternalMeetingOrEvent(string externalMeetingId, string subdomain)
        {
            var externalMeeting = Db.ExternalMeeting.FirstOrDefault(x => x.Id.ToString() == externalMeetingId);
            if (externalMeeting == null)
                throw new RecordNotFoundException("Meeting not found");

            externalMeeting.IsCancelled = true;
            Db.ExternalMeeting.Update(externalMeeting);

            // Get all the bookedMeeting Under the esternal meeting
            var bookedMeetings = await Db.BookedExternalMeeting.Where(x => x.ExternalMeetingId == externalMeeting.Id).ToListAsync();
            foreach (var bookedMeeting in bookedMeetings)
            {
                // Call the cancel booked meeting method
                var response = await CancelBookedExternalMeeting(bookedMeeting.Id.ToString(), "Cancelled by owner", subdomain);
                if (response != "Success")
                    throw new OperationFailedException("Unable to cancel booked meeting");
            }

            // Call RTC endpoint to cancel meeting but first check if themeeting exists
            var path = _rtcEndpoints.CreateMeeting + "/" + $"{externalMeeting.MeetingId}";
            var cancelRes = await _apiCallService.MakeApiCallAsync<object, CreateMeetingResponse>(_rtcEndpoints.BaseUrl, path, Method.Get);
            if (cancelRes.Success)
            {
                var cancelMeetingResponse = await _apiCallService.MakeApiCallAsync<object, CreateMeetingResponse>(_rtcEndpoints.BaseUrl, path, Method.Delete);
                if (!cancelMeetingResponse.Success)
                    _logService.LogError("Failed to cancel meeting on RTC", cancelMeetingResponse.Message);
            }

            var res = await Db.SaveChangesAsync();

            // Triigger a background task that will run at the end of the can book date to update the timebreakdowna and selected time slots
            var scheduleName = Db.PersonalSchedule
              .Where(x => x.UserId == externalMeeting.CreatedBy && x.Id == externalMeeting.PersonalScheduleId)
              .Select(y => y.ScheduleName).FirstOrDefault();

            var personalSchs = await Db.PersonalSchedule
                .Where(x => x.ScheduleName == scheduleName && (!string.IsNullOrEmpty(x.TimeBreakDown) || !string.IsNullOrEmpty(x.SelectedTimeSlots))).ToListAsync();

            return res > 0;
        }
        #endregion

        #region Records
        public record SendMailRecord(string invitee, string email, string template,
            string subject, string message, ExternalMeeting externalMeeting,
            DateTime meetingDate, string fullName, string cancelLink, string rescheduleLink, MemoryStream stream = null);
        #endregion

        #region Add data to redis cache
        /// <summary>
        /// Add data to redis - Background job
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="req"></param>
        /// <param name="redisKey"></param>
        /// <param name="meetings"></param>
        /// <returns></returns>
        private async Task AddDataToRedis<T>(object req, string redisKey, T meetings)
        {
            var redisRes = await _redisCacheService.SetDataAsync(redisKey, meetings, DateTimeOffset.Now.AddDays(1));
            if (!redisRes)
                _logService.LogTypeResponse(req, meetings, nameof(AddDataToRedis), "Adding response to Redis failed after retries");
        }
        #endregion

        #region Test Reschedule meeting background job
        public async Task TestRescheduleMeetingBK()
        {
            await _backGroundService.ResheduleMeetingsThatDidntHappen(new List<string> { "localhost" });
        }
        #endregion

        #region Convert HTML to PDF
        /// <summary>
        /// Converts HTML content to PDF document
        /// </summary>
        /// <param name="model">HTML content and optional filename</param>
        /// <returns>PDF document as byte array in a GenericResponse</returns>
        public async Task<GenericResponse> ConvertHtmlToPdf(ViewModel.HtmlToPdfViewModel model)
        {
            try
            {
                // Use the cross-platform compatible method from PdfService
                byte[] pdfBytes = await _pdfService.GeneratePdfAsync(model.HtmlContent);
                if (pdfBytes == null || pdfBytes.Length == 0)
                {
                    _logService.LogError("PDF Generation Failed", "Generated PDF has no content");
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to convert HTML to PDF",
                        DevResponseMessage = "Generated PDF has no content"
                    };
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "HTML converted to PDF successfully",
                    Data = new
                    {
                        FileName = $"{model.Filename ?? "document"}.pdf",
                        ContentType = "application/pdf",
                        FileContents = pdfBytes
                    }
                };
            }
            catch (System.DllNotFoundException ex)
            {
                _logService.LogError("PDF Generation Failed - DLL Not Found", ex.Message);
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "PDF generation service is currently unavailable",
                    DevResponseMessage = $"Native DLL not found: {ex.Message}. Make sure libwkhtmltox.dll is properly installed."
                };
            }
            catch (System.Exception ex)
            {
                _logService.LogError("PDF Generation Failed", ex.Message);
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while generating the PDF",
                    DevResponseMessage = ex.Message
                };
            }
        }
        #endregion

        #region Get meetings that have happened
        /// <summary>
        /// Mark meeting as happened
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetMeetingsThatHappened(SalesActivityParams model)
        {
            var response = new List<SalesActivityResponseDtoModel>();
            var meets = new List<CalenderMeeting>();
            var EndDate = Convert.ToDateTime(model.EndDate, CultureInfo.CurrentCulture);
            var StartDate = Convert.ToDateTime(model.StartDate, CultureInfo.CurrentCulture);
            if (model.StartDate != null && model.EndDate != null)
            {

                meets = await Db.CalenderMeetings.Where(x => x.CreatedBy.ToString() == model.UserId
                                                 && x.ScheduleType == CalenderScheduleType.Meeting
                                                 && x.StartDate >= StartDate
                                                 && x.EndDate <= EndDate
                                                 && x.IsCancelled == false
                                                 && x.HasThisMeetingHappened)
                                                .ToListAsync();
            }

            for (var date = StartDate.Date; date.Date <= EndDate.Date; date = date.AddDays(1))
            {
                var salesActivity = new SalesActivityResponseDtoModel();

                var numberOfDateMeetings = meets.Where(x => x.StartDate.Date == date.Date)
                    .Select(m => m).Count();

                response.Add(
                    new SalesActivityResponseDtoModel
                    {
                        Activity = "Meeting",
                        Day = date.ToString("s"),
                        Value = numberOfDateMeetings
                    }
                );
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "List of meetings that has happened retrieved successfully",
                Data = response
            };

        }
        #endregion
    }
}
