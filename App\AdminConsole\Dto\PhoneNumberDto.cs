using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Jobid.App.AdminConsole.Enums;
using Newtonsoft.Json;

namespace Jobid.App.AdminConsole.Dto
{
    public class CreatePhoneNumberDto
    {
        [Required]
        public string Number { get; set; }

        public string Description { get; set; }

        [Required]
        public List<PhoneNumberCapabilityType> Capabilities { get; set; }

        public decimal PricePerMinute { get; set; }
    }

    public class UpdatePhoneNumberDto
    {
        [Required]
        public Guid Id { get; set; }

        public string Description { get; set; }

        public bool IsActive { get; set; }

        public decimal PricePerMinute { get; set; }
    }

    public class AssignPhoneNumberUsersDto
    {
        [Required]
        public Guid PhoneNumberId { get; set; }

        [Required]
        public List<string> UserIds { get; set; }

        [JsonIgnore]
        public string CreatedBy { get; set; }
    }

    public class PhoneNumberResponseDto
    {
        public Guid Id { get; set; }
        public string Number { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public decimal Balance { get; set; }
        public decimal PricePerMinute { get; set; }
        public List<PhoneNumberCapabilityType> Capabilities { get; set; }
        public List<string> AssignedUserIds { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
    public class InitiateCallDto
    {
        [Required]
        public Guid PhoneNumberId { get; set; }

        [Required]
        public string ToNumber { get; set; }

        [Required]
        public string FromNumber { get; set; }

        public bool RecordCall { get; set; }

        public bool EnableTranscription { get; set; }

        public string CallerUserId { get; set; }

        public string TwilioConferenceName { get; set; }

        public string Subdomain { get; set; }
    }
    public class CallResponseDto
    {
        public string CallId { get; set; }
        public string Status { get; set; }
        public string WebSocketUrl { get; set; }
        public string Token { get; set; }
    }

    public class AcceptInboundCallDto
    {
        [Required]
        public Guid CallId { get; set; }

        [Required]
        public string UserId { get; set; }

        public string UserName { get; set; }

        public Platform Platform { get; set; } = Platform.WEB;
    }

    public class PurgePhoneNumberDataDto
    {
        [Required]
        public Guid PhoneNumberId { get; set; }

        [Required]
        public string Reason { get; set; }
    }

    public class PurchasePhoneNumberDto
    {
        public string AreaCode { get; set; }

        [Required]
        public string Country { get; set; }

        public string Description { get; set; }

        [Required]
        public List<PhoneNumberCapabilityType> Capabilities { get; set; }

        public decimal PricePerMinute { get; set; }

        // The specific phone number to purchase (from GetAvailablePhoneNumbers)
        public string PhoneNumber { get; set; }

        [JsonIgnore]
        public string Subdomain { get; set; }
    }

    public class RegisterPhoneNumberDto
    {
        [Required]
        public string TenantId { get; set; }

        [Required]
        [Phone]
        public string Number { get; set; }

        public string Description { get; set; }

        [Required]
        public List<PhoneNumberCapabilityType> Capabilities { get; set; }
        public decimal PricePerMinute { get; set; }
    }

    public class GetAvailablePhoneNumbersDto
    {
        [Required]
        public string CountryCode { get; set; }

        public string AreaCode { get; set; }

        public int Limit { get; set; } = 20;

        public bool RequireVoiceCapability { get; set; } = true;

        public bool RequireSmsCapability { get; set; } = false;
    }

    public class AvailablePhoneNumberDto
    {
        public string PhoneNumber { get; set; }
        public string FriendlyName { get; set; }
        public List<string> Capabilities { get; set; }
        public string Region { get; set; }
        public string Locality { get; set; }
        public string NumberType { get; set; }
        public decimal? MonthlyPrice { get; set; }
        public string Currency { get; set; }
    }

    // Twilio PSTN specific DTOs
    public class OutboundCallDto
    {
        [Required]
        public string CallerNumber { get; set; }

        [Required]
        public string CalleeNumber { get; set; }

        [Required]
        public Guid UserId { get; set; }

        [Required]
        public Guid TenantId { get; set; }

        public string Purpose { get; set; }

        public bool RecordCall { get; set; } = false;
    }

    public class InboundCallDto
    {
        [Required]
        public string From { get; set; }

        [Required]
        public string To { get; set; }

        [Required]
        public string CallSid { get; set; }

        public string Direction { get; set; }

        public string ForwardedFrom { get; set; }

        public string CallerName { get; set; }
    }

    public class CallStatusDto
    {
        [Required]
        public string CallSid { get; set; }
        [Required]
        public string CallStatus { get; set; }

        public string Direction { get; set; }

        public string From { get; set; }

        public string To { get; set; }

        public int? CallDuration { get; set; }

        public DateTime? Timestamp { get; set; }
    }

    // WebRTC related DTOs
    public class WebRTCAccessTokenRequestDto
    {
        [Required]
        public string UserId { get; set; }

        public string DisplayName { get; set; }
    }

    public class WebRTCCallRequestDto
    {
        [Required]
        public string UserId { get; set; }

        [Required]
        public Guid FromNumberId { get; set; }

        [Required]
        [Phone]
        public string ToNumber { get; set; }

        public string UserDisplayName { get; set; }

        public int MaxParticipants { get; set; } = 2;

        public bool EnableRecording { get; set; } = false;

        public bool EnableAI { get; set; } = false;

        public string Subdomain { get; set; }

        // Add platform information for push notifications
        public Platform Platform { get; set; } = Platform.WEB; // "ios", "android", "web"
    }

    public class WebRTCAgentRegistrationDto
    {
        [Required]
        public string UserId { get; set; }

        [Required]
        public bool Available { get; set; }
    }

    public class WebRTCTwiMLRequestDto
    {
        public string To { get; set; }
        public string From { get; set; }
        public string CallSid { get; set; }
    }

    public class PhoneNumberEndCallDto
    {
        [Required]
        public string CallSid { get; set; }

        public string UserId { get; set; }

        public string Reason { get; set; } = "user_ended";

        public string Subdomain { get; set; }
    }

    public class ActivateDeactivatePhoneNumberDto
    {
        [Required]
        public Guid PhoneNumberId { get; set; }

        [Required]
        public bool IsActive { get; set; }

        public string Reason { get; set; }

        [JsonIgnore]
        public string UpdatedBy { get; set; }
    }
}