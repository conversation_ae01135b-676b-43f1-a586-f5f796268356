# Phone Number Activation/Deactivation Endpoint

## Overview
This endpoint allows you to activate or deactivate phone numbers in the system. When a phone number is deactivated, it will stop receiving calls through the Twilio webhook. When reactivated, it will resume normal operation.

## Endpoint Details

**URL:** `PUT /api/PhoneNumber/activate-deactivate`

**Authentication:** Required (Bearer token)

**Content-Type:** `application/json`

## Request Body

```json
{
  "phoneNumberId": "guid",
  "isActive": boolean,
  "reason": "string (optional)"
}
```

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| phoneNumberId | GUID | Yes | The unique identifier of the phone number to activate/deactivate |
| isActive | boolean | Yes | `true` to activate, `false` to deactivate |
| reason | string | No | Optional reason for the activation/deactivation |

## Response

### Success Response (200 OK)
```json
{
  "responseCode": "200",
  "responseMessage": "Phone number activated successfully",
  "data": {
    "phoneNumberId": "guid",
    "number": "+1234567890",
    "isActive": true,
    "updatedAt": "2024-01-01T12:00:00Z",
    "reason": "Reactivating for new campaign"
  }
}
```

### Error Responses

#### Phone Number Not Found (404)
```json
{
  "responseCode": "404",
  "responseMessage": "Phone number not found"
}
```

#### Already in Requested State (400)
```json
{
  "responseCode": "400",
  "responseMessage": "Phone number is already active"
}
```

## Examples

### Activate a Phone Number
```bash
curl -X PUT "https://api.example.com/api/PhoneNumber/activate-deactivate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumberId": "123e4567-e89b-12d3-a456-************",
    "isActive": true,
    "reason": "Reactivating for new campaign"
  }'
```

### Deactivate a Phone Number
```bash
curl -X PUT "https://api.example.com/api/PhoneNumber/activate-deactivate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumberId": "123e4567-e89b-12d3-a456-************",
    "isActive": false,
    "reason": "Campaign ended"
  }'
```

## Behavior

### When Activating a Phone Number:
1. Sets `IsActive` to `true` in the database
2. Updates the `UpdatedAt` timestamp
3. If the phone number has a Twilio SID, restores the original webhook URL
4. Returns success response with updated phone number details

### When Deactivating a Phone Number:
1. Sets `IsActive` to `false` in the database
2. Updates the `UpdatedAt` timestamp
3. If the phone number has a Twilio SID, sets the voice URL to a default Twilio demo URL
4. Returns success response with updated phone number details

## Notes

- The endpoint handles both local database updates and Twilio configuration changes
- If Twilio operations fail, the local database changes will still be applied, but warnings will be logged
- The `UpdatedBy` field is automatically set from the authenticated user's ID
- Phone numbers that are already in the requested state will return a 400 error

## Testing

You can test this endpoint using the provided unit tests in `Tests/PhoneNumberActivationTest.cs` or by using the examples above with a valid authentication token.
