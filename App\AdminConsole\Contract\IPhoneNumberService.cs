using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Dto.AI;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IPhoneNumberService
    {
        // Existing methods
        Task<GenericResponse> PurchasePhoneNumber(PurchasePhoneNumberDto model);
        Task<GenericResponse> RegisterExistingNumber(RegisterPhoneNumberDto model);
        Task<GenericResponse> AssignUsers(AssignPhoneNumberUsersDto model);
        Task<GenericResponse> ActivateDeactivatePhoneNumber(ActivateDeactivatePhoneNumberDto model);
        Task<GenericResponse> GetPhoneNumber(Guid id);
        Task<GenericResponse> GetPhoneNumbers(string tenantId);
        Task<GenericResponse> HandleInboundCall(string fromNumber, string toNumber, string subdomain, string callSid);
        Task<string> GenerateInboundCallTallBackTwiML(string callSid, string fromNumber, string toNumber, string subdomain);
        Task<string> GenerateInboundCallTwiML(string callSid, string fromNumber, string toNumber, string subdomain);
        Task<GenericResponse> AcceptInboundCall(AcceptInboundCallDto model);
        Task<GenericResponse> UpdateCallStatus(string callId, string status, string subdoamin);
        Task<GenericResponse> UpdateCallStatusByCallId(Guid callId, string status, string? subdomain = null);
        Task<GenericResponse> PurgePhoneNumberData(PurgePhoneNumberDataDto model);
        Task<GenericResponse> GetCallHistory(Guid phoneNumberId, DateTime? startDate, DateTime? endDate);
        Task<GenericResponse> GetCallRecording(string callId);
        Task<GenericResponse> GetCallTranscription(string callId);
        Task<GenericResponse> GetAvailablePhoneNumbers(string countryCode, string areaCode = null, string phoneType = null);
        Task<GenericResponse> GetCallAnalytics(Guid phoneNumberId, DateTime? startDate, DateTime? endDate);

        // WebRTC methods
        Task<GenericResponse> GenerateWebRTCAccessToken(string userId, Platform platform, string displayName = null);
        Task<string> GenerateOutboundWebRTCTwiML(string toNumber, string callSid);
        Task<string> GenerateOutboundWebRTCTwiMLForUser(string userId, string callSid);
        Task<string> GenerateInboundWebRTCTwiML(string fromNumber, string toNumber, string callSid, string subdomain);
        Task<GenericResponse> RegisterAgentForWebRTCCalls(string userId, bool available);
        Task<GenericResponse> InitiateWebRTCCall(WebRTCCallRequestDto request);
        Task<string> GetAvailableWebRTCAgent(string subdomain);
        Task<string> GetAvailableWebRTCAgentForNumber(string toNumber, string subdomain);
        Task<string> HandleWebRTCDialStatus(string dialCallStatus, string callSid, string agentId);
        Task<GenericResponse> EndCall(PhoneNumberEndCallDto model);

        // Telepony methods
        Task<GenericResponse?> GetCompanyByPhoneNumber(string phoneNumber);
        Task<GenericResponse> GetAssignedPhoneNumbersForUser(string userId);
        Task<GenericResponse> AddCallTransaction(CallTransactionDto model);

        // AI methods
        Task<GenericResponse> AddOrUpdateCompanySummarizedInfo(CompanySummarizedInfoDto model);
        Task<GenericResponse> GetCompanySummarizedInfo(Guid companyId);
        Task<GenericResponse> AddOrUpdateCompanyFilesSummary(CompanyFileSummaryDto model);
        Task<GenericResponse> GetCompanyFilesSummary(Guid companyId);

        // Signal R Tests
        Task SendTestIncomingCallEvents(string userId, object payload);
        Task SendTestIncomingCallEventsViaNotificationHub(string userId, object payload);
    }
}