using System;
using System.Threading.Tasks;
using Xunit;
using Microsoft.EntityFrameworkCore;
using Jobid.App.AdminConsole.Services;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Models.Phone;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using AutoMapper;
using Jobid.App.Notification.Hubs;
using Jobid.App.AdminConsole.Hubs;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.AdminConsole.Contract;
using Moq;

namespace Tests
{
    public class PhoneNumberActivationTest
    {
        private JobProDbContext GetInMemoryContext()
        {
            var options = new DbContextOptionsBuilder<JobProDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            return new JobProDbContext(options);
        }

        [Fact]
        public async Task ActivateDeactivatePhoneNumber_ShouldActivateInactiveNumber()
        {
            // Arrange
            using var context = GetInMemoryContext();
            using var publicContext = GetInMemoryContext();

            var phoneNumber = new PhoneNumber
            {
                Id = Guid.NewGuid(),
                Number = "+1234567890",
                IsActive = false,
                IsRegistered = true,
                CountryCode = "US",
                NumberType = Jobid.App.AdminConsole.Enums.PhoneNumberType.Local
            };

            context.PhoneNumbers.Add(phoneNumber);
            await context.SaveChangesAsync();

            // Create mock dependencies (simplified for test)
            var configuration = new ConfigurationBuilder().Build();
            var hubContext = new Mock<IHubContext<NotificationHub>>().Object;
            var cache = new MemoryCache(new MemoryCacheOptions());
            var mapper = new Mock<IMapper>().Object;
            var wikiFileService = new Mock<IWikiFileService>().Object;
            var callHubContext = new Mock<IHubContext<CallHub>>().Object;
            var twilioService = new Mock<ITwilioService>().Object;

            var service = new PhoneNumberService(
                configuration,
                context,
                hubContext,
                cache,
                publicContext,
                mapper,
                wikiFileService,
                callHubContext,
                twilioService
            );

            var request = new ActivateDeactivatePhoneNumberDto
            {
                PhoneNumberId = phoneNumber.Id,
                IsActive = true,
                Reason = "Test activation"
            };

            // Act
            var result = await service.ActivateDeactivatePhoneNumber(request);

            // Assert
            Assert.Equal("200", result.ResponseCode);
            Assert.Contains("activated", result.ResponseMessage);

            var updatedPhoneNumber = await context.PhoneNumbers.FindAsync(phoneNumber.Id);
            Assert.True(updatedPhoneNumber.IsActive);
            Assert.NotNull(updatedPhoneNumber.UpdatedAt);
        }

        [Fact]
        public async Task ActivateDeactivatePhoneNumber_ShouldReturnNotFound_WhenPhoneNumberDoesNotExist()
        {
            // Arrange
            using var context = GetInMemoryContext();
            using var publicContext = GetInMemoryContext();

            // Create mock dependencies (simplified for test)
            var configuration = new ConfigurationBuilder().Build();
            var hubContext = new Mock<IHubContext<NotificationHub>>().Object;
            var cache = new MemoryCache(new MemoryCacheOptions());
            var mapper = new Mock<IMapper>().Object;
            var wikiFileService = new Mock<IWikiFileService>().Object;
            var callHubContext = new Mock<IHubContext<CallHub>>().Object;
            var twilioService = new Mock<ITwilioService>().Object;

            var service = new PhoneNumberService(
                configuration,
                context,
                hubContext,
                cache,
                publicContext,
                mapper,
                wikiFileService,
                callHubContext,
                twilioService
            );

            var request = new ActivateDeactivatePhoneNumberDto
            {
                PhoneNumberId = Guid.NewGuid(),
                IsActive = true,
                Reason = "Test activation"
            };

            // Act
            var result = await service.ActivateDeactivatePhoneNumber(request);

            // Assert
            Assert.Equal("404", result.ResponseCode);
            Assert.Equal("Phone number not found", result.ResponseMessage);
        }
    }
}
